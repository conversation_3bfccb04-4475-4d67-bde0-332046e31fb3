<?php

namespace App\Services;

use App\Models\Tenant;
use App\Models\TenantImport;

class TenantImportService
{
    /**
     * Parse name from various formats
     */
    public function parseName(string $nameString): array
    {
        if (empty(trim($nameString))) {
            return ['first_name' => 'No Name', 'last_name' => ''];
        }

        $name = trim($nameString);

        // Handle formats like "Sellathurai Siyamalar / Rahunthan"
        if (str_contains($name, '/')) {
            $parts = explode('/', $name);
            $name = trim($parts[0]); // Take the first part
        }

        // Handle formats like "Ganesan & Akhana"
        if (str_contains($name, '&')) {
            $parts = explode('&', $name);
            $name = trim($parts[0]); // Take the first part
        }

        // Handle formats like "Fazia (Sharon) Singh" - remove parentheses content
        $name = preg_replace('/\([^)]*\)/', '', $name);
        $name = trim($name);

        // Handle names with multiple spaces and clean them up
        $name = preg_replace('/\s+/', ' ', $name);

        // Split by spaces
        $parts = array_filter(explode(' ', $name));

        if (count($parts) === 1) {
            return ['first_name' => ucwords(strtolower($parts[0])), 'last_name' => ''];
        }

        if (count($parts) === 2) {
            return [
                'first_name' => ucwords(strtolower($parts[0])),
                'last_name' => ucwords(strtolower($parts[1]))
            ];
        }

        // For names with 3+ parts, first is first name, rest is last name
        $firstName = array_shift($parts);
        $lastName = implode(' ', $parts);

        return [
            'first_name' => ucwords(strtolower($firstName)),
            'last_name' => ucwords(strtolower($lastName))
        ];
    }

    /**
     * Format phone number for messaging (Twilio-compatible format).
     *
     * Handles Canadian phone numbers and international numbers for development.
     */
    public function formatPhoneNumber(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // Trim whitespace
        $number = trim($phone);

        // If number already starts with +, validate and return as is (for international dev numbers)
        if (str_starts_with($number, '+')) {
            // Remove any non-digit characters except the + prefix
            $cleanNumber = '+' . preg_replace('/[^0-9]/', '', substr($number, 1));

            // Basic validation: should have at least 10 digits after +
            if (strlen($cleanNumber) >= 11) {
                return $cleanNumber;
            }

            // If invalid international format, fall through to Canadian formatting
            $number = substr($number, 1); // Remove + and process as domestic
        }

        // Remove common extensions and extra text first
        $number = preg_replace('/\s*(ext|extension|x)\s*\d+.*$/i', '', $number);

        // Remove all non-digit characters
        $number = preg_replace('/[^0-9]/', '', $number);

        // Handle empty or too short numbers
        if (strlen($number) < 10) {
           return null;
        }

        // Handle Canadian/US numbers
        if (strlen($number) == 10) {
            // 10 digits: assume Canadian number without country code
            // Example: 6473492669 → +16473492669
            return '+1' . $number;
        } elseif (strlen($number) == 11 && str_starts_with($number, '1')) {
            // 11 digits starting with 1: Canadian/US number with country code
            // Example: 16473492669 → +16473492669
            return '+' . $number;
        } elseif (strlen($number) > 11) {
            // More than 11 digits: assume international number (for dev)
            // Example: 6285758866491 → +6285758866491
            return '+' . $number;
        } else {
            // 11 digits not starting with 1: add Canadian country code
            // This handles edge cases
            return '+1' . $number;
        }
    }

    /**
     * Validate email address
     */
    public function validateEmail(?string $email): ?string
    {
        if (empty($email)) {
            return null;
        }

        $email = trim($email);
        
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return strtolower($email);
        }
        
        return null;
    }

    /**
     * Check for duplicate tenants with unit number priority logic
     */
    public function findDuplicateTenant(array $tenantData): ?Tenant
    {
        $unitNumber = $tenantData['unit_number'] ?? null;
        $primaryEmail = $tenantData['email'] ?? null;
        $secondaryEmail = $tenantData['secondary_email'] ?? null;

        // Priority 1: Unit number checking (if unit number is not empty)
        if (!empty($unitNumber)) {
            // Find tenants with the same unit number
            $tenantsWithSameUnit = Tenant::where('unit_number', $unitNumber)->get();

            if ($tenantsWithSameUnit->isNotEmpty()) {
                // Check if any tenant with same unit number has matching email
                foreach ($tenantsWithSameUnit as $tenant) {
                    // Check if primary email matches
                    if (!empty($primaryEmail) &&
                        ($tenant->email === $primaryEmail || $tenant->secondary_email === $primaryEmail)) {
                        return $tenant;
                    }

                    // Check if secondary email matches
                    if (!empty($secondaryEmail) &&
                        ($tenant->email === $secondaryEmail || $tenant->secondary_email === $secondaryEmail)) {
                        return $tenant;
                    }
                }

                // If unit number matches but email doesn't match, treat as new tenant
                // (return null to indicate no duplicate found)
                return null;
            }
        }

        // Priority 2: Fallback to original phone/email matching logic for tenants without unit numbers
        $primaryPhone = $tenantData['mobile_phone'] ?? null;
        $secondaryPhone = $tenantData['secondary_mobile_phone'] ?? null;
        $primaryWhatsApp = $tenantData['whatsapp_number'] ?? null;
        $secondaryWhatsApp = $tenantData['secondary_whatsapp_number'] ?? null;

        // Phone number pair checking
        if (!empty($primaryPhone)) {
            if (!empty($secondaryPhone)) {
                // Both primary and secondary phone exist - check as a pair
                $contact = Contact::where('mobile_phone', $primaryPhone)
                    ->where('secondary_mobile_phone', $secondaryPhone)
                    ->first();
                if ($contact) {
                    return $contact;
                }

                // If exact pair doesn't match, check if primary phone matches and secondary is empty
                $contact = Contact::where('mobile_phone', $primaryPhone)
                    ->where(function($query) {
                        $query->whereNull('secondary_mobile_phone')
                              ->orWhere('secondary_mobile_phone', '');
                    })
                    ->first();
                if ($contact) {
                    return $contact;
                }
            } else {
                // Only primary phone exists - match primary phone only
                $contact = Contact::where('mobile_phone', $primaryPhone)->first();
                if ($contact) {
                    return $contact;
                }
            }
        } elseif (!empty($secondaryPhone)) {
            // Only secondary phone exists - match secondary phone only
            $contact = Contact::where('secondary_mobile_phone', $secondaryPhone)->first();
            if ($contact) {
                return $contact;
            }
        }

        // WhatsApp number pair checking (only if no phone numbers available)
        if (empty($primaryPhone) && empty($secondaryPhone)) {
            if (!empty($primaryWhatsApp)) {
                if (!empty($secondaryWhatsApp)) {
                    // Both primary and secondary WhatsApp exist - check as a pair
                    $contact = Contact::where('whatsapp_number', $primaryWhatsApp)
                        ->where('secondary_whatsapp_number', $secondaryWhatsApp)
                        ->first();
                    if ($contact) {
                        return $contact;
                    }

                    // If exact pair doesn't match, check if primary WhatsApp matches and secondary is empty
                    $contact = Contact::where('whatsapp_number', $primaryWhatsApp)
                        ->where(function($query) {
                            $query->whereNull('secondary_whatsapp_number')
                                  ->orWhere('secondary_whatsapp_number', '');
                        })
                        ->first();
                    if ($contact) {
                        return $contact;
                    }
                } else {
                    // Only primary WhatsApp exists - match primary WhatsApp only
                    $contact = Contact::where('whatsapp_number', $primaryWhatsApp)->first();
                    if ($contact) {
                        return $contact;
                    }
                }
            } elseif (!empty($secondaryWhatsApp)) {
                // Only secondary WhatsApp exists - match secondary WhatsApp only
                $contact = Contact::where('secondary_whatsapp_number', $secondaryWhatsApp)->first();
                if ($contact) {
                    return $contact;
                }
            }
        }

        // Email pair checking (only for contacts without unit numbers or when unit number logic didn't find a match)
        if (empty($primaryPhone) && empty($secondaryPhone) &&
            empty($primaryWhatsApp) && empty($secondaryWhatsApp)) {

            if (!empty($primaryEmail)) {
                if (!empty($secondaryEmail)) {
                    // Both primary and secondary email exist - check as a pair
                    $contact = Contact::where('email', $primaryEmail)
                        ->where('secondary_email', $secondaryEmail)
                        ->first();
                    if ($contact) {
                        return $contact;
                    }

                    // If exact pair doesn't match, check if primary email matches and secondary is empty
                    $contact = Contact::where('email', $primaryEmail)
                        ->where(function($query) {
                            $query->whereNull('secondary_email')
                                  ->orWhere('secondary_email', '');
                        })
                        ->first();
                    if ($contact) {
                        return $contact;
                    }
                } else {
                    // Only primary email exists - match primary email only
                    $contact = Contact::where('email', $primaryEmail)->first();
                    if ($contact) {
                        return $contact;
                    }
                }
            } elseif (!empty($secondaryEmail)) {
                // Only secondary email exists - match secondary email only
                $contact = Contact::where('secondary_email', $secondaryEmail)->first();
                if ($contact) {
                    return $contact;
                }
            }
        }

        return null;
    }

    /**
     * Determine contact methods based on available data
     */
    public function determineContactMethods(array $contactData): array
    {
        return [
            'contact_sms' => !empty($contactData['mobile_phone']) || !empty($contactData['secondary_mobile_phone']),
            'contact_email' => !empty($contactData['email']) || !empty($contactData['secondary_email']),
            'contact_wa' => !empty($contactData['whatsapp_number']) || !empty($contactData['secondary_whatsapp_number']),
        ];
    }

    /**
     * Process and validate contact data
     */
    public function processContactData(array $rawData, array $columnMapping, int $propertyId): array
    {
        $processedData = [];
        $errors = [];

        // Extract name
        $nameField = $columnMapping['name'] ?? null;
        $nameValue = $nameField ? ($rawData[$nameField] ?? '') : '';
        $nameData = $this->parseName($nameValue);
        
        $processedData['first_name'] = $nameData['first_name'];
        $processedData['last_name'] = $nameData['last_name'];

        // Extract unit number
        $unitField = $columnMapping['unit_number'] ?? null;
        $processedData['unit_number'] = $unitField ? trim($rawData[$unitField] ?? '') : '';
        
        // Unit number is optional - no validation needed

        // Process phone numbers
        $phoneFields = ['mobile_phone', 'secondary_mobile_phone', 'whatsapp_number', 'secondary_whatsapp_number'];
        foreach ($phoneFields as $field) {
            $columnName = $columnMapping[$field] ?? null;
            $phoneValue = $columnName ? ($rawData[$columnName] ?? '') : '';
            $processedData[$field] = $this->formatPhoneNumber($phoneValue);
        }

        // Process emails
        $emailFields = ['email', 'secondary_email'];
        foreach ($emailFields as $field) {
            $columnName = $columnMapping[$field] ?? null;
            $emailValue = $columnName ? ($rawData[$columnName] ?? '') : '';
            $processedData[$field] = $this->validateEmail($emailValue);
        }

        // Check if we should skip this contact
        $hasName = $processedData['first_name'] !== 'No Name';
        $hasPhone = !empty($processedData['mobile_phone']);

        if (!$hasName && !$hasPhone) {
            $errors[] = 'Contact must have either a name or phone number';
        }

        // Check if contact has name but no contact information
        if ($hasName) {
            $hasContactInfo = !empty($processedData['mobile_phone']) ||
                            !empty($processedData['secondary_mobile_phone']) ||
                            !empty($processedData['email']) ||
                            !empty($processedData['secondary_email']) ||
                            !empty($processedData['whatsapp_number']) ||
                            !empty($processedData['secondary_whatsapp_number']);

            if (!$hasContactInfo) {
                $errors[] = 'Contact has name but no contact information (phone, email, or WhatsApp)';
            }
        }

        // Determine contact methods
        $contactMethods = $this->determineContactMethods($processedData);
        $processedData = array_merge($processedData, $contactMethods);

        // Set property and status
        $processedData['property_id'] = $propertyId;
        $processedData['status'] = true;

        return [
            'data' => $processedData,
            'errors' => $errors,
            'has_errors' => !empty($errors)
        ];
    }

    /**
     * Check for duplicate within imported data using unit number and email logic
     */
    public function findDuplicateInImportedData(array $contactData, array $processedRows, int $currentIndex): ?int
    {
        $unitNumber = $contactData['unit_number'] ?? null;
        $primaryEmail = $contactData['email'] ?? null;
        $secondaryEmail = $contactData['secondary_email'] ?? null;

        // Only check previous rows (to avoid checking against future rows)
        for ($i = 0; $i < $currentIndex; $i++) {
            $previousRow = $processedRows[$i]['data'];
            $previousUnitNumber = $previousRow['unit_number'] ?? null;
            $previousPrimaryEmail = $previousRow['email'] ?? null;
            $previousSecondaryEmail = $previousRow['secondary_email'] ?? null;

            // If unit number is not empty, check unit number + email logic
            if (!empty($unitNumber) && $unitNumber === $previousUnitNumber) {
                // Check if emails match
                if (!empty($primaryEmail) &&
                    ($primaryEmail === $previousPrimaryEmail || $primaryEmail === $previousSecondaryEmail)) {
                    return $i; // Return index of duplicate row
                }

                if (!empty($secondaryEmail) &&
                    ($secondaryEmail === $previousPrimaryEmail || $secondaryEmail === $previousSecondaryEmail)) {
                    return $i; // Return index of duplicate row
                }
            }
        }

        return null; // No duplicate found
    }

    /**
     * Create contact import records from processed data
     */
    public function createImportRecords(array $processedRows, string $sessionId): array
    {
        $stats = [
            'total' => count($processedRows),
            'valid' => 0,
            'errors' => 0,
            'duplicates' => 0,
            'new' => 0
        ];

        foreach ($processedRows as $index => $row) {
            $importData = [
                'session_id' => $sessionId,
                'raw_data' => $row['raw_data'],
                'validation_errors' => $row['errors'],
                'has_errors' => $row['has_errors'],
            ];

            $importData = array_merge($importData, $row['data']);

            // First check for duplicates within the imported data
            $duplicateIndex = $this->findDuplicateInImportedData($row['data'], $processedRows, $index);
            if ($duplicateIndex !== null) {
                // Mark as duplicate within import data (but not as error)
                $importData['duplicate_action'] = 'skip'; // Skip duplicates within same import
                $importData['duplicate_reason'] = "Duplicate within import data (row " . ($duplicateIndex + 2) . ")"; // +2 because of 0-based index and header row
                $stats['duplicates']++;
            } else {
                // Check for duplicates against existing contacts
                $duplicate = $this->findDuplicateContact($row['data']);
                if ($duplicate) {
                    $importData['existing_contact_id'] = $duplicate->id;
                    $importData['duplicate_action'] = 'update';
                    $importData['duplicate_reason'] = "Matches existing contact: " . $duplicate->first_name . " " . $duplicate->last_name;
                    $stats['duplicates']++;
                } else {
                    $stats['new']++;
                }
            }

            if ($importData['has_errors']) {
                $stats['errors']++;
            } else {
                $stats['valid']++;
            }

            ContactImport::create($importData);
        }

        return $stats;
    }

    /**
     * Import contacts from temporary table to main contacts table
     */
    public function importContacts(string $sessionId): array
    {
        $imports = ContactImport::where('session_id', $sessionId)
            ->where('has_errors', false)
            ->get();

        $stats = [
            'imported' => 0,
            'updated' => 0,
            'failed' => 0,
            'skipped' => 0
        ];

        foreach ($imports as $import) {
            try {
                if ($import->duplicate_action === 'skip') {
                    // Skip duplicates within import data
                    $stats['skipped']++;
                } elseif ($import->existing_contact_id && $import->duplicate_action === 'update') {
                    // Update existing contact with only non-empty values
                    $contact = Contact::find($import->existing_contact_id);
                    if ($contact) {
                        $updateData = $this->prepareUpdateData($import);
                        $contact->update($updateData);
                        $stats['updated']++;
                    } else {
                        $stats['failed']++;
                    }
                } else {
                    // Create new contact
                    Contact::create($import->only([
                        'first_name', 'last_name', 'email', 'secondary_email',
                        'mobile_phone', 'secondary_mobile_phone', 'whatsapp_number',
                        'secondary_whatsapp_number', 'contact_sms', 'contact_wa',
                        'contact_email', 'property_id', 'unit_number', 'status'
                    ]));
                    $stats['imported']++;
                }
            } catch (\Exception) {
                $stats['failed']++;
            }
        }

        // Clean up temporary records
        ContactImport::where('session_id', $sessionId)->delete();

        return $stats;
    }

    /**
     * Prepare update data for duplicate contacts - only update non-empty values
     * and preserve contact method flags (don't set them to false)
     */
    private function prepareUpdateData(ContactImport $import): array
    {
        $updateData = [];

        // Fields that should be updated if they have non-empty values
        $fieldsToUpdate = [
            'first_name', 'last_name', 'email', 'secondary_email',
            'mobile_phone', 'secondary_mobile_phone', 'whatsapp_number',
            'secondary_whatsapp_number', 'property_id', 'unit_number'
        ];

        foreach ($fieldsToUpdate as $field) {
            $newValue = $import->$field;

            // Only update if the new value is not empty
            if (!empty($newValue)) {
                $updateData[$field] = $newValue;
            }
        }

        // Handle contact method flags - only set to true, never to false
        // This preserves existing contact methods and adds new ones
        $contactMethodFlags = ['contact_sms', 'contact_wa', 'contact_email'];

        foreach ($contactMethodFlags as $flag) {
            $newValue = $import->$flag;

            // Only update if new value is true
            // If new value is false, don't include in update (preserves existing value)
            if ($newValue === true) {
                $updateData[$flag] = true;
            }
        }

        // Always update status to active when importing
        $updateData['status'] = true;

        return $updateData;
    }
}

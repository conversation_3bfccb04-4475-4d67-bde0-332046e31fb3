<?php

namespace App\Http\Controllers;

use App\Models\Group;
use App\Models\Tenant;
use App\Models\Property;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class GroupController extends Controller
{
    /**
     * Display a listing of the groups.
     */
    public function index(Request $request): Response
    {
        $search = $request->get('search', '');
        $statusFilter = $request->get('status', []);
        $perPage = $request->get('per_page', 15);
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');

        $query = Group::query();

        // Search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Status filter
        if (!empty($statusFilter)) {
            $statusValues = array_map(function ($status) {
                return $status === 'active' ? 1 : 0;
            }, $statusFilter);
            $query->whereIn('status', $statusValues);
        }

        // Add contacts count
        $query->withCount('contacts');

        // Sorting
        $query->orderBy($sortField, $sortDirection);

        $groups = $query->paginate($perPage)->withQueryString();

        // Get status counts
        $statusCounts = [
            'active' => Group::where('status', true)->count(),
            'inactive' => Group::where('status', false)->count(),
        ];

        return Inertia::render('Groups/Index', [
            'groups' => $groups,
            'sort' => [
                'field' => $sortField,
                'direction' => $sortDirection,
            ],
            'filters' => [
                'search' => $search,
                'status' => $statusFilter,
                'per_page' => $perPage
            ],
            'statusCounts' => $statusCounts,
        ]);
    }

    /**
     * Show the form for creating a new group.
     */
    public function create(): Response
    {
        return Inertia::render('Groups/GroupForm');
    }

    /**
     * Store a newly created group in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:groups,name',
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'status' => 'required|boolean',
        ]);

        $group = Group::create($validated);

        // If it's an AJAX request (from the popup), return JSON
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Group created successfully.',
                'group' => $group
            ]);
        }

        return redirect()->route('groups.index')->with('success', 'Group created successfully.');
    }

    /**
     * Display the specified group.
     */
    public function show(Request $request, Group $group): Response
    {
        $group->load(['contacts.property']);
        $properties = Property::select('id', 'name')->orderBy('name')->get();

        // Get contacts in this group with filtering and pagination
        $sort = $request->input('sort', ['field' => 'first_name', 'direction' => 'asc']);
        $search = $request->input('search');
        $statusFilter = $request->input('status', []);
        $perPage = $request->input('per_page', 10);

        $contactsQuery = $group->contacts()->with('property');

        // Apply search filter
        if ($search) {
            $contactsQuery->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('mobile_phone', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if (!empty($statusFilter)) {
            $statusValues = array_map(function ($status) {
                return $status === 'active' ? 1 : 0;
            }, $statusFilter);
            $contactsQuery->whereIn('status', $statusValues);
        }

        // Apply sorting
        $contactsQuery->orderBy($sort['field'], $sort['direction']);

        $groupContacts = $contactsQuery->paginate($perPage)->withQueryString();

        return Inertia::render('Groups/Show', [
            'group' => $group,
            'properties' => $properties,
            'groupContacts' => $groupContacts,
            'sort' => $sort,
            'filters' => [
                'search' => $search,
                'status' => $statusFilter,
                'per_page' => $perPage
            ],
        ]);
    }

    /**
     * Show the form for editing the specified group.
     */
    public function edit(Group $group): Response
    {
        return Inertia::render('Groups/GroupForm', [
            'group' => $group,
        ]);
    }

    /**
     * Update the specified group in storage.
     */
    public function update(Request $request, Group $group)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:groups,name,' . $group->id,
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'status' => 'required|boolean',
        ]);

        $group->update($validated);

        return redirect()->route('groups.index')->with('success', 'Group updated successfully.');
    }

    /**
     * Remove the specified group from storage.
     */
    public function destroy(Group $group)
    {
        $group->delete();

        return redirect()->route('groups.index')->with('success', 'Group deleted successfully.');
    }

    /**
     * Add multiple contacts to the group.
     */
    public function addContacts(Request $request, Group $group)
    {
        $validated = $request->validate([
            'contact_ids' => 'required|array|min:1',
            'contact_ids.*' => 'exists:tenants,id',
        ]);

        $contactIds = $validated['contact_ids'];

        // Get contacts that are not already in the group
        $existingContactIds = $group->contacts()->pluck('contacts.id')->toArray();
        $newContactIds = array_diff($contactIds, $existingContactIds);

        if (empty($newContactIds)) {
            $message = 'All selected contacts are already in this group.';

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                ], 422);
            }

            return redirect()->route('groups.show', $group)->with('error', $message);
        }

        // Add the new contacts to the group
        $group->contacts()->attach($newContactIds);

        $addedCount = count($newContactIds);
        $skippedCount = count($contactIds) - $addedCount;

        $message = $addedCount === 1
            ? '1 contact added to the group successfully.'
            : "{$addedCount} contacts added to the group successfully.";

        if ($skippedCount > 0) {
            $message .= " {$skippedCount} contacts were already in the group.";
        }

        // Return JSON for AJAX requests, redirect for regular requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'added_count' => $addedCount,
                'skipped_count' => $skippedCount,
            ]);
        }

        return redirect()->route('groups.show', $group)->with('success', $message);
    }

    /**
     * Remove a contact from the group.
     */
    public function removeContact(Group $group, Tenant $contact)
    {
        // Check if the contact is actually in the group
        if (!$group->contacts()->where('contacts.id', $contact->id)->exists()) {
            return redirect()->route('groups.show', $group)->with('error', 'Contact is not in this group.');
        }

        // Remove the contact from the group
        $group->contacts()->detach($contact->id);

        return redirect()->route('groups.show', $group)->with('success', 'Contact removed from group successfully.');
    }
}

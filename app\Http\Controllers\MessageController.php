<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\Tenant;
use App\Models\Group;
use App\Models\Property;
use App\Models\MessageLog;
use App\Models\MessageRecipient;
use App\Jobs\SendMessageJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use HTMLPurifier;
use HTMLPurifier_Config;

class MessageController extends Controller
{
    /**
     * Sanitize HTML content for email messages.
     */
    private function sanitizeHtmlContent(string $content): string
    {
        $config = HTMLPurifier_Config::createDefault();

        // Allow safe HTML tags for email content including images
        $config->set('HTML.Allowed', 'p,br,strong,b,em,i,u,a[href|title|target],ul,ol,li,h1,h2,h3,h4,h5,h6,img[src|alt|width|height|title],div[class],span[class]');

        // Allow safe protocols for links and images
        $config->set('URI.AllowedSchemes', 'http,https,mailto');

        // Disable cache for simplicity (you can enable it in production)
        $config->set('Cache.DefinitionImpl', null);

        $purifier = new HTMLPurifier($config);
        return $purifier->purify($content);
    }
    /**
     * Display a listing of messages.
     */
    public function index(Request $request)
    {
        // Get sorting parameters
        $sort = $request->input('sort', ['field' => 'created_at', 'direction' => 'desc']);
        $perPage = $request->input('per_page', 15);

        $query = Message::with(['user', 'recipients'])
            ->withCount(['recipients as total_recipients']);

        // Apply filters
        if ($request->filled('type')) {
            $type = $request->type;
            $query->where(function ($q) use ($type) {
                // Check both legacy type field and new channels field
                $q->where('type', $type)
                  ->orWhereJsonContains('channels', $type);
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('subject', 'like', '%' . $request->search . '%')
                  ->orWhere('content', 'like', '%' . $request->search . '%')
                  ->orWhere('title', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Apply sorting
        $sortField = $sort['field'] ?? 'created_at';
        $sortDirection = $sort['direction'] ?? 'desc';

        // Map frontend field names to database column names if needed
        $fieldMapping = [
            'title' => 'title',
            'type' => 'type',
            'status' => 'status',
            'created_at' => 'created_at',
            'recipient_count' => 'recipient_count', // Use the actual column name
        ];

        $dbField = $fieldMapping[$sortField] ?? 'created_at';
        $query->orderBy($dbField, $sortDirection);

        $messages = $query->paginate($perPage)->withQueryString();

        // Get status counts for filtering
        $statusCounts = [
            'all' => Message::count(),
            'draft' => Message::where('status', 'draft')->count(),
            'queued' => Message::where('status', 'queued')->count(),
            'sending' => Message::where('status', 'sending')->count(),
            'completed' => Message::where('status', 'completed')->count(),
            'paused' => Message::where('status', 'paused')->count(),
            'failed' => Message::where('status', 'failed')->count(),
            'cancelled' => Message::where('status', 'cancelled')->count(),
        ];

        return Inertia::render('Messages/Index', [
            'messages' => $messages,
            'sort' => $sort,
            'filters' => $request->only(['type', 'status', 'search', 'date_from', 'date_to', 'per_page']),
            'statusCounts' => $statusCounts,
        ]);
    }

    /**
     * Show the form for creating a new message.
     */
    public function create()
    {
        $contacts = Tenant::where('status', true)->with('property')->get()->map(function ($contact) {
            return [
                'id' => $contact->id,
                'name' => $contact->full_name,
                'email' => $contact->email ?? '',
                'phone' => $contact->mobile_phone ?? '',
                'property' => $contact->property?->name ?? '',
                'contact_sms' => $contact->contact_sms,
                'contact_email' => $contact->contact_email,
                'contact_wa' => $contact->contact_wa,
            ];
        });

        $properties = Property::withCount(['contacts' => function ($query) {
            $query->where('status', true);
        }])->get();

        $groups = Group::where('status', true)->withCount(['contacts' => function ($query) {
            $query->where('status', true);
        }])->get();

        $totalContactCount = Tenant::where('status', true)->count();

        return inertia('Messages/MessageForm', [
            'contacts' => $contacts,
            'properties' => $properties,
            'groups' => $groups,
            'totalContactCount' => $totalContactCount,
        ]);
    }

    /**
     * Store a newly created message.
     */
    public function store(Request $request)
    {
        // Check if saving as draft to determine validation rules
        $saveAsDraft = $request->boolean('save_as_draft', false);

        if ($saveAsDraft) {
            // For drafts, only title is required
            $request->validate([
                'title' => 'required|string|max:255',
                'channels' => 'nullable|array',
                'channels.*' => 'nullable|string|in:sms,email,whatsapp',
                'subject' => 'nullable|string|max:255',
                'sms_content' => 'nullable|string|max:160',
                'email_content' => 'nullable|string|max:10000',
                'whatsapp_content' => 'nullable|string|max:10000',
                'recipients' => 'nullable|array',
                'recipients.*.id' => 'nullable|string',
                'recipients.*.type' => 'nullable|string|in:contact,property,group',
                'save_as_draft' => 'boolean',
                'select_all_contacts' => 'nullable|boolean',
            ]);
        } else {
            // For sending, validate channels and their content
            $request->validate([
                'title' => 'required|string|max:255',
                'channels' => 'required|array|min:1',
                'channels.*' => 'required|string|in:sms,email,whatsapp',
                'subject' => 'nullable|string|max:255',
                'sms_content' => 'nullable|string|max:160',
                'email_content' => 'nullable|string|max:10000',
                'whatsapp_content' => 'nullable|string|max:10000',
                'recipients' => 'required_unless:select_all_contacts,true|array',
                'recipients.*.id' => 'required_unless:select_all_contacts,true|string',
                'recipients.*.type' => 'required_unless:select_all_contacts,true|string|in:contact,property,group',
                'save_as_draft' => 'boolean',
                'select_all_contacts' => 'boolean',
            ], [
                'channels.required' => 'Please select at least one channel.',
                'channels.min' => 'Please select at least one channel.',
                'recipients.*.type.in' => 'Please select valid recipients. Each recipient must be a contact, property, or group.',
                'recipients.*.type.required' => 'Recipient type is required for each selected recipient.',
                'recipients.*.id.required' => 'Recipient ID is required for each selected recipient.',
                'recipients.required' => 'Please select at least one recipient or choose "Send to All Contacts".',
            ]);

            // Custom validation for channel-specific content
            $channels = $request->channels ?? [];
            $errors = [];

            if (in_array('email', $channels)) {
                if (empty($request->subject)) {
                    $errors['subject'] = 'Subject is required for email messages.';
                }
                if (empty($request->email_content)) {
                    $errors['email_content'] = 'Email content is required when Email channel is selected.';
                }
            }

            if (in_array('sms', $channels) && empty($request->sms_content)) {
                $errors['sms_content'] = 'SMS content is required when SMS channel is selected.';
            }

            if (in_array('whatsapp', $channels) && empty($request->whatsapp_content)) {
                $errors['whatsapp_content'] = 'WhatsApp content is required when WhatsApp channel is selected.';
            }

            if (!empty($errors)) {
                return back()->withErrors($errors)->withInput();
            }
        }

        try {
            DB::beginTransaction();

            // Sanitize email content if provided
            $emailContent = $request->email_content ?? '';
            if (!empty($emailContent)) {
                $emailContent = $this->sanitizeHtmlContent($emailContent);
            }

            // Create message with draft status initially
            $message = Message::create([
                'title' => $request->title,
                'channels' => $request->channels ?? [],
                'subject' => $request->subject,
                'sms_content' => $request->sms_content,
                'email_content' => $emailContent,
                'whatsapp_content' => $request->whatsapp_content,
                'status' => 'draft', // Always start as draft
                'user_id' => Auth::id(),
            ]);

            // Process recipients using unified method
            $this->processRecipients(
                $message,
                $request->recipients,
                $request->boolean('select_all_contacts'),
                $saveAsDraft
            );

            // Check if there are valid recipients when not saving as draft
            if (!$saveAsDraft) {
                // Refresh the message to get the updated recipient_count
                $message->refresh();
                $recipientCount = $message->recipient_count ?? 0;

                if ($recipientCount === 0) {
                    // Rollback transaction and delete the message
                    DB::rollBack();
                    $message->delete();
                    return back()->with('error', 'Cannot send message: No valid recipients found. Please check that the selected contacts have valid contact information for ' . strtolower(ucfirst($request->type ?? 'sms')) . ' messaging and that the contact method is enabled.');
                }

                // Update status to queued only if we have valid recipients
                $message->update(['status' => 'queued']);
            }

            // Log message creation
            MessageLog::createLog(
                $message->id,
                'created',
                'Message created by ' . Auth::user()->name,
                [
                    'user_id' => Auth::id(),
                    'user_name' => Auth::user()->name,
                    'message_channels' => $message->getEnabledChannels(),
                    'recipient_count' => $message->recipient_count,
                    'created_at' => now()->toISOString(),
                    'is_draft' => $saveAsDraft,
                    'is_multi_channel' => $message->isMultiChannel(),
                    'metadata' => [
                        'subject' => $message->subject,
                        'content_length' => strlen($message->content ?? ''),
                        'scheduled_at' => $message->scheduled_at?->toISOString()
                    ]
                ]
            );

            if (!$saveAsDraft) {
                // Queue the message for sending
                SendMessageJob::dispatch($message);
                MessageLog::createLog(
                    $message->id,
                    'queued',
                    'Message queued for sending',
                    [
                        'user_id' => Auth::id(),
                        'queued_at' => now()->toISOString(),
                        'message_channels' => $message->getEnabledChannels(),
                        'recipient_count' => $message->recipient_count,
                        'is_multi_channel' => $message->isMultiChannel(),
                        'scheduled_at' => $message->scheduled_at?->toISOString()
                    ]
                );
            } else {
                // Log draft save
                MessageLog::createLog(
                    $message->id,
                    'draft_saved',
                    'Message saved as draft by ' . Auth::user()->name,
                    [
                        'user_id' => Auth::id(),
                        'user_name' => Auth::user()->name,
                        'message_channels' => $message->getEnabledChannels(),
                        'recipient_count' => $message->recipient_count,
                        'saved_at' => now()->toISOString(),
                        'is_multi_channel' => $message->isMultiChannel(),
                        'metadata' => [
                            'subject' => $message->subject,
                            'content_length' => strlen($message->content ?? ''),
                            'has_recipients' => $message->recipient_count > 0
                        ]
                    ]
                );
            }

            DB::commit();

            // Calculate statistics for success message
            $recipientCount = $message->recipient_count ?? 0;
            $channels = $message->getEnabledChannels();
            $channelsText = count($channels) > 1
                ? 'Multi-channel'
                : ucfirst($channels[0] ?? 'Message');

            // Redirect based on whether message is draft or not
            if ($request->save_as_draft) {
                $successMessage = "Message saved as draft with {$recipientCount} recipient" . ($recipientCount !== 1 ? 's' : '') . ".";
                return redirect()->route('messages.index')
                    ->with('success', $successMessage);
            }

            $successMessage = "{$channelsText} message queued for sending to {$recipientCount} recipient" . ($recipientCount !== 1 ? 's' : '') . ".";
            return redirect()->route('messages.show', $message)
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating message: ' . $e->getMessage());
            return back()->with('error', 'An error occurred while creating the message.');
        }
    }

    /**
     * Display the specified message.
     */
    public function show(Message $message)
    {
        $message->load([
            'user',
            'recipients.contact',
        ]);

        // Load logs with pagination
        $logs = $message->logs()
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Calculate delivery stats
        $stats = [
            'total' => $message->recipient_count,
            'sent' => $message->sent_count,
            'failed' => $message->failed_count,
            'pending' => $message->pending_count,
            'progress' => $message->recipient_count > 0
                ? round(($message->sent_count + $message->failed_count) / $message->recipient_count * 100)
                : 0
        ];

        return Inertia::render('Messages/Show', [
            'message' => $message,
            'logs' => $logs,
            'deliveryStats' => $stats,
            'refreshInterval' => in_array($message->status, ['queued', 'sending', 'paused']) ? 5000 : null
        ]);
    }

    /**
     * Show the form for editing a draft message.
     */
    public function edit(Message $message)
    {
        $contacts = Tenant::where('status', true)->with('property')->get()->map(function ($contact) {
            return [
                'id' => $contact->id,
                'name' => $contact->full_name,
                'email' => $contact->email ?? '',
                'phone' => $contact->mobile_phone ?? '',
                'property' => $contact->property?->name ?? '',
                'contact_sms' => $contact->contact_sms,
                'contact_email' => $contact->contact_email,
                'contact_wa' => $contact->contact_wa,
            ];
        });

        $properties = Property::withCount(['contacts' => function ($query) {
            $query->where('status', true);
        }])->get();

        $groups = Group::where('status', true)->withCount(['contacts' => function ($query) {
            $query->where('status', true);
        }])->get();

        $totalContactCount = Tenant::where('status', true)->count();

        return inertia('Messages/MessageForm', [
            'message' => $message->load('recipients'),
            'contacts' => $contacts,
            'properties' => $properties,
            'groups' => $groups,
            'totalContactCount' => $totalContactCount,
        ]);
    }

    /**
     * Update the specified draft message.
     */
    public function update(Request $request, Message $message)
    {
        // Only allow updating of draft messages
        if ($message->status !== 'draft') {
            return back()->with('error', 'Only draft messages can be updated.');
        }

        // Check if saving as draft to determine validation rules
        $saveAsDraft = $request->boolean('save_as_draft', false);

        if ($saveAsDraft) {
            // For drafts, only title is required
            $validated = $request->validate([
                'title' => ['required', 'string', 'max:255'],
                'channels' => ['nullable', 'array'],
                'channels.*' => ['nullable', Rule::in(['sms', 'email', 'whatsapp'])],
                'subject' => ['nullable', 'string', 'max:255'],
                'sms_content' => ['nullable', 'string', 'max:160'],
                'email_content' => ['nullable', 'string', 'max:10000'],
                'whatsapp_content' => ['nullable', 'string', 'max:10000'],
                'recipients' => ['nullable', 'array'],
                'recipients.*.type' => ['nullable', Rule::in(['contact', 'property', 'group'])],
                'recipients.*.id' => ['nullable', 'integer'],
                'save_as_draft' => ['boolean'],
            ]);
        } else {
            // For sending, validate channels and their content
            $validated = $request->validate([
                'title' => ['required', 'string', 'max:255'],
                'channels' => ['required', 'array', 'min:1'],
                'channels.*' => ['required', Rule::in(['sms', 'email', 'whatsapp'])],
                'subject' => ['nullable', 'string', 'max:255'],
                'sms_content' => ['nullable', 'string', 'max:160'],
                'email_content' => ['nullable', 'string', 'max:10000'],
                'whatsapp_content' => ['nullable', 'string', 'max:10000'],
                'recipients' => ['required', 'array', 'min:1'],
                'recipients.*.type' => ['required', Rule::in(['contact', 'property', 'group'])],
                'recipients.*.id' => ['required', 'integer'],
                'save_as_draft' => ['boolean'],
            ], [
                'channels.required' => 'Please select at least one channel.',
                'channels.min' => 'Please select at least one channel.',
                'recipients.*.type.in' => 'Please select valid recipients. Each recipient must be a contact, property, or group.',
                'recipients.*.type.required' => 'Recipient type is required for each selected recipient.',
                'recipients.*.id.required' => 'Recipient ID is required for each selected recipient.',
                'recipients.required' => 'Please select at least one recipient or choose "Send to All Contacts".',
                'recipients.min' => 'Please select at least one recipient.',
            ]);

            // Custom validation for channel-specific content
            $channels = $validated['channels'] ?? [];
            $errors = [];

            if (in_array('email', $channels)) {
                if (empty($validated['subject'])) {
                    $errors['subject'] = 'Subject is required for email messages.';
                }
                if (empty($validated['email_content'])) {
                    $errors['email_content'] = 'Email content is required when Email channel is selected.';
                }
            }

            if (in_array('sms', $channels) && empty($validated['sms_content'])) {
                $errors['sms_content'] = 'SMS content is required when SMS channel is selected.';
            }

            if (in_array('whatsapp', $channels) && empty($validated['whatsapp_content'])) {
                $errors['whatsapp_content'] = 'WhatsApp content is required when WhatsApp channel is selected.';
            }

            if (!empty($errors)) {
                return back()->withErrors($errors)->withInput();
            }
        }

        DB::beginTransaction();
        try {
            // Sanitize email content if provided
            $emailContent = $validated['email_content'] ?? '';
            if (!empty($emailContent)) {
                $emailContent = $this->sanitizeHtmlContent($emailContent);
            }

            // Clear existing recipients first
            $message->recipients()->delete();

            // Update message first to set channels before processing recipients
            $message->update([
                'title' => $validated['title'],
                'channels' => $validated['channels'] ?? [],
                'subject' => $validated['subject'] ?? null,
                'sms_content' => $validated['sms_content'] ?? null,
                'email_content' => $emailContent,
                'whatsapp_content' => $validated['whatsapp_content'] ?? null,
                'status' => $saveAsDraft ? 'draft' : 'queued',
            ]);

            // Process recipients using unified method
            $this->processRecipients(
                $message,
                $validated['recipients'] ?? null,
                false, // select_all_contacts not supported in update
                $saveAsDraft
            );

            // Check if there are valid recipients when not saving as draft
            if (!$saveAsDraft) {
                // Refresh the message to get the updated recipient_count
                $message->refresh();
                $recipientCount = $message->recipient_count ?? 0;

                if ($recipientCount === 0) {
                    // Rollback transaction and return error
                    DB::rollBack();
                    $channels = $validated['channels'] ?? [];
                    $channelsText = implode(', ', array_map('strtoupper', $channels));
                    return back()->with('error', "Cannot send message: No valid recipients found. Please check that the selected contacts have valid contact information for {$channelsText} messaging and that the contact methods are enabled.");
                }
            }

            if (!($validated['save_as_draft'] ?? false)) {
                // Message is being sent (draft -> queued)
                MessageLog::createLog(
                    $message->id,
                    'updated',
                    'Message updated and prepared for sending by ' . Auth::user()->name,
                    [
                        'user_id' => Auth::id(),
                        'user_name' => Auth::user()->name,
                        'message_channels' => $message->getEnabledChannels(),
                        'recipient_count' => $message->recipient_count,
                        'updated_at' => now()->toISOString(),
                        'action' => 'draft_to_send',
                        'is_multi_channel' => $message->isMultiChannel(),
                        'metadata' => [
                            'subject' => $message->subject,
                            'content_length' => strlen($message->content ?? ''),
                            'scheduled_at' => $message->scheduled_at?->toISOString()
                        ]
                    ]
                );

                // Queue the message for sending
                SendMessageJob::dispatch($message);
                MessageLog::createLog(
                    $message->id,
                    'queued',
                    'Message queued for sending',
                    [
                        'user_id' => Auth::id(),
                        'queued_at' => now()->toISOString(),
                        'message_channels' => $message->getEnabledChannels(),
                        'recipient_count' => $message->recipient_count,
                        'is_multi_channel' => $message->isMultiChannel(),
                        'scheduled_at' => $message->scheduled_at?->toISOString()
                    ]
                );
            } else {
                // Message is being updated as draft
                MessageLog::createLog(
                    $message->id,
                    'draft_updated',
                    'Draft message updated by ' . Auth::user()->name,
                    [
                        'user_id' => Auth::id(),
                        'user_name' => Auth::user()->name,
                        'message_channels' => $message->getEnabledChannels(),
                        'recipient_count' => $message->recipient_count,
                        'updated_at' => now()->toISOString(),
                        'action' => 'draft_update',
                        'is_multi_channel' => $message->isMultiChannel(),
                        'metadata' => [
                            'subject' => $message->subject,
                            'content_length' => strlen($message->content ?? ''),
                            'has_recipients' => $message->recipient_count > 0,
                            'scheduled_at' => $message->scheduled_at?->toISOString()
                        ]
                    ]
                );
            }

            DB::commit();

            // Calculate statistics for success message
            $recipientCount = $message->recipient_count ?? 0;
            $channels = $message->getEnabledChannels();
            $channelsText = count($channels) > 1
                ? 'Multi-channel'
                : ucfirst($channels[0] ?? 'Message');

            // Redirect based on whether the message is saved as draft or queued
            if ($validated['save_as_draft'] ?? false) {
                $successMessage = "Draft message updated successfully with {$recipientCount} recipient" . ($recipientCount !== 1 ? 's' : '') . ".";
                return redirect()->route('messages.index')
                    ->with('success', $successMessage);
            }

            $successMessage = "{$channelsText} message updated and queued for sending to {$recipientCount} recipient" . ($recipientCount !== 1 ? 's' : '') . ".";
            return redirect()->route('messages.show', $message)
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'An error occurred while updating the message.');
        }
    }

    /**
     * Remove the specified message.
     */
    public function destroy(Message $message)
    {
        // Only allow deletion of draft, completed, failed, or cancelled messages
        if (!in_array($message->status, ['draft', 'completed', 'failed', 'cancelled'])) {
            return back()->with('error', 'Cannot delete a message that is currently being sent.');
        }

        $message->delete();

        return redirect()->route('messages.index')
            ->with('success', 'Message deleted successfully.');
    }

    /**
     * Process recipients and create message recipient records.
     * Handles both individual recipients and select_all_contacts scenarios.
     */
    private function processRecipients(Message $message, ?array $recipients = null, bool $selectAllContacts = false, bool $saveAsDraft = false)
    {
        $recipientRecords = [];
        $totalRecipients = 0;

        // Skip recipient processing if saving as draft
        if ($saveAsDraft) {
            $message->update(['recipient_count' => 0]);
            return;
        }

        // Handle select all contacts scenario
        if ($selectAllContacts) {
            // Get all active contacts that have at least one enabled channel
            $channels = $message->getEnabledChannels();
            $contacts = Tenant::where('status', true)
                ->where(function ($query) use ($channels) {
                    foreach ($channels as $channel) {
                        switch ($channel) {
                            case 'sms':
                                $query->orWhere('contact_sms', true);
                                break;
                            case 'email':
                                $query->orWhere('contact_email', true);
                                break;
                            case 'whatsapp':
                                $query->orWhere('contact_wa', true);
                                break;
                        }
                    }
                })
                ->get();

            // Create message recipients for all matching contacts
            $recipientRecords = $contacts->flatMap(function ($contact) use ($message, $channels) {
                $records = [];
                foreach ($channels as $channel) {
                    $recipientValue = $contact->getPreferredContactMethod($channel);
                    if ($recipientValue) {
                        $records[] = [
                            'message_id' => $message->id,
                            'contact_id' => $contact->id,
                            'recipient_type' => 'contact',
                            'recipient_value' => $recipientValue,
                            'channel' => $channel,
                            'status' => 'pending',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }
                }
                return $records;
            })->toArray();

            $totalRecipients = count($recipientRecords);
        } elseif ($recipients) {
            // Handle individual recipients
            $channels = $message->getEnabledChannels();

            foreach ($recipients as $recipient) {
                if ($recipient['type'] === 'contact') {
                    $contact = Tenant::find($recipient['id']);
                    if ($contact && $contact->status) {
                        foreach ($channels as $channel) {
                            $recipientValue = $contact->getPreferredContactMethod($channel);
                            if ($recipientValue !== null) {
                                $recipientRecords[] = [
                                    'message_id' => $message->id,
                                    'contact_id' => $contact->id,
                                    'recipient_type' => 'contact',
                                    'recipient_value' => $recipientValue,
                                    'channel' => $channel,
                                    'status' => 'pending',
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ];
                                $totalRecipients++;
                            }
                        }

                        // Log if contact has no valid methods for any channel
                        $hasValidMethod = false;
                        foreach ($channels as $channel) {
                            if ($contact->getPreferredContactMethod($channel) !== null) {
                                $hasValidMethod = true;
                                break;
                            }
                        }
                        if (!$hasValidMethod) {
                            $this->logSkippedContact($message, $contact, 'contact');
                        }
                    }
                } elseif ($recipient['type'] === 'property') {
                    $property = Property::with('contacts')->find($recipient['id']);
                    if ($property) {
                        foreach ($property->contacts()->where('status', true)->get() as $contact) {
                            foreach ($channels as $channel) {
                                $recipientValue = $contact->getPreferredContactMethod($channel);
                                if ($recipientValue !== null) {
                                    $recipientRecords[] = [
                                        'message_id' => $message->id,
                                        'contact_id' => $contact->id,
                                        'recipient_type' => 'property',
                                        'recipient_value' => $recipientValue,
                                        'channel' => $channel,
                                        'status' => 'pending',
                                        'created_at' => now(),
                                        'updated_at' => now(),
                                    ];
                                    $totalRecipients++;
                                }
                            }

                            // Log if contact has no valid methods for any channel
                            $hasValidMethod = false;
                            foreach ($channels as $channel) {
                                if ($contact->getPreferredContactMethod($channel) !== null) {
                                    $hasValidMethod = true;
                                    break;
                                }
                            }
                            if (!$hasValidMethod) {
                                $this->logSkippedContact($message, $contact, 'property', $recipient['id']);
                            }
                        }
                    }
                } elseif ($recipient['type'] === 'group') {
                    $group = Group::with('contacts')->find($recipient['id']);
                    if ($group) {
                        foreach ($group->contacts()->where('status', true)->get() as $contact) {
                            foreach ($channels as $channel) {
                                $recipientValue = $contact->getPreferredContactMethod($channel);
                                if ($recipientValue !== null) {
                                    $recipientRecords[] = [
                                        'message_id' => $message->id,
                                        'contact_id' => $contact->id,
                                        'recipient_type' => 'group',
                                        'recipient_value' => $recipientValue,
                                        'channel' => $channel,
                                        'status' => 'pending',
                                        'created_at' => now(),
                                        'updated_at' => now(),
                                    ];
                                    $totalRecipients++;
                                }
                            }

                            // Log if contact has no valid methods for any channel
                            $hasValidMethod = false;
                            foreach ($channels as $channel) {
                                if ($contact->getPreferredContactMethod($channel) !== null) {
                                    $hasValidMethod = true;
                                    break;
                                }
                            }
                            if (!$hasValidMethod) {
                                $this->logSkippedContact($message, $contact, 'group', $recipient['id']);
                            }
                        }
                    }
                }
            }
        }

        // Bulk insert recipients
        if (!empty($recipientRecords)) {
            DB::table('message_recipients')->insert($recipientRecords);
        }

        // Update message recipient count
        $message->update(['recipient_count' => $totalRecipients]);
    }

    /**
     * Log skipped contacts with detailed information
     */
    private function logSkippedContact(Message $message, Contact $contact, string $recipientType, ?int $propertyId = null)
    {
        $channels = $message->getEnabledChannels();
        $channelsText = implode(', ', $channels);

        $logData = [
            'message_id' => $message->id,
            'contact_id' => $contact->id,
            'contact_name' => $contact->full_name,
            'message_channels' => $channels,
            'has_email' => $contact->contact_email && !empty($contact->email),
            'has_sms' => $contact->contact_sms && !empty($contact->mobile_phone),
            'has_whatsapp' => $contact->contact_wa && !empty($contact->whatsapp_number),
            'phone' => $contact->mobile_phone,
            'email' => $contact->email,
            'whatsapp' => $contact->whatsapp_number,
            'secondary_phone' => $contact->secondary_mobile_phone,
            'secondary_email' => $contact->secondary_email,
            'secondary_whatsapp' => $contact->secondary_whatsapp_number
        ];

        if ($propertyId) {
            $logData['property_id'] = $propertyId;
        }

        $logMessage = $recipientType === 'property'
            ? "Property contact {$contact->full_name} skipped - no valid contact methods for channels: {$channelsText}"
            : "Contact {$contact->full_name} skipped - no valid contact methods for channels: {$channelsText}";

        Log::warning($logMessage, $logData);

        MessageLog::createLog(
            $message->id,
            'recipient_skipped',
            $logMessage,
            $logData
        );
    }

    /**
     * Pause message sending.
     */
    public function pause(Message $message)
    {
        if (!$message->canBePaused()) {
            return back()->with('error', 'Message cannot be paused in its current state.');
        }

        $message->update(['status' => 'paused']);

        MessageLog::createLog(
            $message->id,
            'paused',
            'Message sending paused by ' . Auth::user()->name,
            [
                'user_id' => Auth::id(),
                'user_name' => Auth::user()->name,
                'paused_at' => now()->toISOString(),
                'previous_status' => $message->getOriginal('status'),
                'sent_count' => $message->sent_count,
                'failed_count' => $message->failed_count,
                'pending_count' => $message->pending_count
            ]
        );

        return back()->with('success', 'Message sending has been paused.');
    }

    /**
     * Resume message sending.
     */
    public function resume(Message $message)
    {
        if (!$message->canBeResumed()) {
            return back()->with('error', 'Message cannot be resumed in its current state.');
        }

        $message->update(['status' => 'queued']);

        MessageLog::createLog(
            $message->id,
            'resumed',
            'Message sending resumed by ' . Auth::user()->name,
            [
                'user_id' => Auth::id(),
                'user_name' => Auth::user()->name,
                'resumed_at' => now()->toISOString(),
                'previous_status' => $message->getOriginal('status'),
                'sent_count' => $message->sent_count,
                'failed_count' => $message->failed_count,
                'pending_count' => $message->pending_count
            ]
        );

        // Dispatch job to continue sending
        SendMessageJob::dispatch($message);

        return back()->with('success', 'Message sending has been resumed.');
    }

    /**
     * Cancel message sending.
     */
    public function cancel(Message $message)
    {
        if (!$message->canBeCancelled()) {
            return back()->with('error', 'Message cannot be cancelled in its current state.');
        }

        $message->update(['status' => 'cancelled']);

        MessageLog::createLog(
            $message->id,
            'cancelled',
            'Message sending cancelled by ' . Auth::user()->name,
            [
                'user_id' => Auth::id(),
                'user_name' => Auth::user()->name,
                'cancelled_at' => now()->toISOString(),
                'previous_status' => $message->getOriginal('status'),
                'sent_count' => $message->sent_count,
                'failed_count' => $message->failed_count,
                'pending_count' => $message->pending_count
            ]
        );

        return back()->with('success', 'Message sending has been cancelled.');
    }

    /**
     * Retry failed message deliveries.
     */
    public function retry(Message $message)
    {
        $failedRecipients = $message->recipients()->where('status', 'failed')->get();

        if ($failedRecipients->isEmpty()) {
            return back()->with('error', 'No failed recipients to retry.');
        }

        // Reset failed recipients to pending and decrement failed count
        DB::transaction(function () use ($message, $failedRecipients) {
            // Reset each recipient
            foreach ($failedRecipients as $recipient) {
                $recipient->update([
                    'status' => 'pending',
                    'error_message' => null,
                    'external_id' => null,
                    'sent_at' => null,
                    'delivered_at' => null,
                    'read_at' => null,
                    'metadata' => null
                ]);

                MessageLog::createLog(
                    $message->id,
                    'retry',
                    'Retrying failed recipient ' . $recipient->recipient_value,
                    [
                        'recipient_id' => $recipient->id,
                        'contact_id' => $recipient->contact_id,
                        'previous_error' => $recipient->getOriginal('error_message'),
                        'retry_at' => now()->toISOString(),
                        'user_id' => Auth::id(),
                        'user_name' => Auth::user()->name
                    ],
                    $recipient->id
                );
            }

            // Update message status and counts
            $message->update([
                'status' => 'queued',
                'failed_count' => $message->failed_count - $failedRecipients->count(),
                'completed_at' => null
            ]);

            // Log the retry attempt
            MessageLog::createLog(
                $message->id,
                'retry_initiated',
                'Retrying ' . $failedRecipients->count() . ' failed recipients',
                [
                    'user_id' => Auth::id(),
                    'user_name' => Auth::user()->name,
                    'retry_at' => now()->toISOString(),
                    'recipient_count' => $failedRecipients->count(),
                    'previous_status' => $message->getOriginal('status'),
                    'message_channels' => $message->getEnabledChannels(),
                    'is_multi_channel' => $message->isMultiChannel()
                ]
            );

            // Dispatch job to retry failed messages
            SendMessageJob::dispatch($message);
        });

        $retryCount = $failedRecipients->count();
        $successMessage = "Retrying {$retryCount} failed recipient" . ($retryCount !== 1 ? 's' : '') . ". Messages have been queued for retry.";
        return back()->with('success', $successMessage);
    }

    /**
     * Retry a specific recipient.
     */
    public function retryRecipient(Message $message, MessageRecipient $recipient)
    {
        if ($recipient->message_id !== $message->id) {
            return back()->with('error', 'Invalid recipient for this message.');
        }

        if ($recipient->status !== 'failed') {
            return back()->with('error', 'Only failed recipients can be retried.');
        }

        DB::transaction(function () use ($message, $recipient) {
            $previousError = $recipient->error_message;
            $originalMessageStatus = $message->status;

            // Reset recipient to pending
            $recipient->update([
                'status' => 'pending',
                'error_message' => null,
                'external_id' => null,
                'sent_at' => null,
                'delivered_at' => null,
                'read_at' => null,
                'metadata' => null
            ]);

            // Update message counts
            $message->decrement('failed_count');

            MessageLog::createLog(
                $message->id,
                'retry_recipient_started',
                'Starting retry for recipient ' . $recipient->recipient_value,
                [
                    'recipient_id' => $recipient->id,
                    'contact_id' => $recipient->contact_id,
                    'previous_error' => $previousError,
                    'retry_at' => now()->toISOString(),
                    'user_id' => Auth::id(),
                    'user_name' => Auth::user()->name,
                    'recipient_channel' => $recipient->channel,
                    'original_message_status' => $originalMessageStatus
                ],
                $recipient->id
            );

            // Send to recipient directly using MessageSendingService
            try {
                $messageSendingService = app(\App\Services\MessageSendingService::class);
                $result = $messageSendingService->sendToRecipient($message, $recipient);

                if ($result['success']) {
                    // Mark as sent
                    $recipient->markAsSent($result['external_id'] ?? null, $result['metadata'] ?? []);

                    // Update message counts
                    $message->increment('sent_count');

                    MessageLog::createLog(
                        $message->id,
                        'retry_recipient_success',
                        "Retry successful for recipient {$recipient->recipient_value}",
                        array_merge($result['metadata'] ?? [], [
                            'recipient_id' => $recipient->id,
                            'contact_id' => $recipient->contact_id,
                            'external_id' => $result['external_id'] ?? null,
                            'sent_at' => now()->toISOString(),
                            'recipient_channel' => $recipient->channel,
                            'delivery_status' => 'sent',
                            'retry_attempt' => true
                        ]),
                        $recipient->id
                    );
                } else {
                    // Mark as failed again
                    $recipient->markAsFailed($result['error'] ?? 'Unknown error', $result['metadata'] ?? []);

                    // Update message counts
                    $message->increment('failed_count');

                    MessageLog::createLog(
                        $message->id,
                        'retry_recipient_failed',
                        "Retry failed for recipient {$recipient->recipient_value}: {$result['error']}",
                        array_merge($result['metadata'] ?? [], [
                            'recipient_id' => $recipient->id,
                            'contact_id' => $recipient->contact_id,
                            'error' => $result['error'],
                            'failed_at' => now()->toISOString(),
                            'recipient_channel' => $recipient->channel,
                            'delivery_status' => 'failed',
                            'retry_attempt' => true
                        ]),
                        $recipient->id
                    );
                }

                // Recalculate message counts to ensure accuracy
                $message->recalculateCounts();

            } catch (\Exception $e) {
                // Mark as failed due to exception
                $recipient->markAsFailed($e->getMessage());
                $message->increment('failed_count');

                MessageLog::createLog(
                    $message->id,
                    'retry_recipient_failed',
                    "Retry failed for recipient {$recipient->recipient_value}: {$e->getMessage()}",
                    [
                        'recipient_id' => $recipient->id,
                        'contact_id' => $recipient->contact_id,
                        'error' => $e->getMessage(),
                        'error_class' => get_class($e),
                        'failed_at' => now()->toISOString(),
                        'recipient_channel' => $recipient->channel,
                        'delivery_status' => 'failed',
                        'retry_attempt' => true
                    ],
                    $recipient->id
                );

                // Recalculate message counts
                $message->recalculateCounts();
            }
        });

        return back()->with('success', 'Recipient retry completed. Check the message details for results.');
    }

    /**
     * Search for recipients (contacts and properties).
     */
    public function searchRecipients(Request $request)
    {
        $search = $request->input('search', '');
        $type = $request->input('type');
        $recipientType = $request->input('recipient_type', 'contacts');
        $limit = $request->input('limit', 10);

        $response = ['contacts' => [], 'properties' => [], 'groups' => []];

        if ($recipientType === 'contacts') {
            $response['contacts'] = Tenant::query()
                ->with('property')
                ->where('status', true)
                ->when($search, function ($query) use ($search) {
                    $query->where(function ($q) use ($search) {
                        $q->where('first_name', 'like', "%{$search}%")
                          ->orWhere('last_name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('mobile_phone', 'like', "%{$search}%");
                    });
                })
                ->when($type, function ($query) use ($type) {
                    switch ($type) {
                        case 'sms':
                            $query->where('contact_sms', true);
                            break;
                        case 'email':
                            $query->where('contact_email', true);
                            break;
                        case 'whatsapp':
                            $query->where('contact_wa', true);
                            break;
                    }
                })
                ->limit($limit)
                ->get()
                ->map(function ($contact) use ($type) {
                    return [
                        'id' => $contact->id,
                        'name' => $contact->full_name,
                        'email' => $contact->email,
                        'phone' => $contact->mobile_phone,
                        'property' => $contact->property ? $contact->property->name : null,
                        'type' => 'contact',
                        'contact_sms' => $contact->contact_sms,
                        'contact_email' => $contact->contact_email,
                        'contact_wa' => $contact->contact_wa,
                    ];
                });
        } elseif ($recipientType === 'properties') {
            $response['properties'] = Property::query()
                ->with(['contacts' => function ($query) use ($type) {
                    $query->where('status', true)
                        ->when($type, function ($q) use ($type) {
                            switch ($type) {
                                case 'sms':
                                    $q->where('contact_sms', true);
                                    break;
                                case 'email':
                                    $q->where('contact_email', true);
                                    break;
                                case 'whatsapp':
                                    $q->where('contact_wa', true);
                                    break;
                            }
                        });
                }])
                ->when($search, function ($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%");
                })
                ->limit($limit)
                ->get()
                ->map(function ($property) {
                    return [
                        'id' => $property->id,
                        'name' => $property->name,
                        'type' => 'property',
                        'contacts_count' => $property->contacts->count(),
                    ];
                });
        } elseif ($recipientType === 'groups') {
            $response['groups'] = Group::query()
                ->where('status', true)
                ->with(['contacts' => function ($query) use ($type) {
                    $query->where('status', true)
                        ->when($type, function ($q) use ($type) {
                            switch ($type) {
                                case 'sms':
                                    $q->where('contact_sms', true);
                                    break;
                                case 'email':
                                    $q->where('contact_email', true);
                                    break;
                                case 'whatsapp':
                                    $q->where('contact_wa', true);
                                    break;
                            }
                        });
                }])
                ->when($search, function ($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%");
                })
                ->limit($limit)
                ->get()
                ->map(function ($group) {
                    return [
                        'id' => $group->id,
                        'name' => $group->name,
                        'color' => $group->color,
                        'type' => 'group',
                        'contacts_count' => $group->contacts->count(),
                    ];
                });
        }

        return response()->json($response);
    }

    /**
     * Get all contacts that can receive messages of the specified type.
     */
    public function getAllContacts(Request $request)
    {
        $type = $request->input('type');
        $recipientType = $request->input('recipient_type', 'contacts');
        
        if ($recipientType === 'contacts') {
            $query = Tenant::query()->where('status', true);
            
            switch ($type) {
                case 'sms':
                    $query->where('contact_sms', true);
                    break;
                case 'email':
                    $query->where('contact_email', true);
                    break;
                case 'whatsapp':
                    $query->where('contact_wa', true);
                    break;
            }

            $recipients = $query->get()->map(function ($contact) {
                return [
                    'type' => 'contact',
                    'id' => (string) $contact->id,
                    'name' => $contact->full_name,
                ];
            });
        } else {
            $recipients = Property::with(['contacts' => function ($query) use ($type) {
                $query->where('status', true)
                    ->when($type, function ($q) use ($type) {
                        switch ($type) {
                            case 'sms':
                                $q->where('contact_sms', true);
                                break;
                            case 'email':
                                $q->where('contact_email', true);
                                break;
                            case 'whatsapp':
                                $q->where('contact_wa', true);
                                break;
                        }
                    });
            }])
            ->get()
            ->filter(function ($property) {
                return $property->contacts->count() > 0;
            })
            ->map(function ($property) {
                return [
                    'type' => 'property',
                    'id' => (string) $property->id,
                    'name' => $property->name,
                ];
            });
        }

        return response()->json($recipients);
    }
}

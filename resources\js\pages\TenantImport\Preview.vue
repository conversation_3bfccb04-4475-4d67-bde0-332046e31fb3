<template>
  <AppLayout :breadcrumbs="breadcrumbs">
    <Head title="Preview Import Data" />
    
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <div class="flex items-center justify-between">
        <Heading
          :title="currentStep === 7 ? 'Import Complete' : 'Preview Import Data'"
          :description="currentStep === 7 ? 'Your contacts have been successfully imported.' : 'Review the processed contact data before final import.'"
        />
        <div v-if="currentStep !== 7" class="flex justify-between items-center">
          <div class="text-sm font-medium">
            {{ stats.valid }} valid contacts ready for import
          </div>
          <div class="flex gap-2 ml-1">
            <Button variant="outline" @click="cancelImport">
              Cancel Import
            </Button>
            <Button @click="showConfirmDialog = true" class="border">
              Start Import
            </Button>
          </div>
        </div>
      </div>

      <FlashAlert />

      <!-- Import Step Progress -->
      <ImportStepProgress :current-step="currentStep" :steps="steps" />
      
      <!-- Enhanced Statistics Cards -->
      <div v-if="currentStep !== 7" class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg border border-blue-200 dark:border-blue-800 p-6 transition-all duration-200 hover:shadow-md">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">Total Contacts</p>
              <p class="text-3xl font-bold text-blue-900 dark:text-blue-100">{{ stats.total }}</p>
            </div>
            <div class="p-3 bg-blue-200 dark:bg-blue-800 rounded-full">
              <Users class="h-6 w-6 text-blue-700 dark:text-blue-300" />
            </div>
          </div>
          <div class="mt-3 text-xs text-blue-600 dark:text-blue-400">
            Ready for processing
          </div>
        </div>

        <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-lg border border-green-200 dark:border-green-800 p-6 transition-all duration-200 hover:shadow-md">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-green-700 dark:text-green-300 mb-1">Valid Contacts</p>
              <p class="text-3xl font-bold text-green-900 dark:text-green-100">{{ stats.valid }}</p>
            </div>
            <div class="p-3 bg-green-200 dark:bg-green-800 rounded-full">
              <Check class="h-6 w-6 text-green-700 dark:text-green-300" />
            </div>
          </div>
          <div class="mt-3 text-xs text-green-600 dark:text-green-400">
            {{ Math.round((stats.valid / stats.total) * 100) }}% success rate
          </div>
        </div>

        <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900 rounded-lg border border-red-200 dark:border-red-800 p-6 transition-all duration-200 hover:shadow-md">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-red-700 dark:text-red-300 mb-1">Errors</p>
              <p class="text-3xl font-bold text-red-900 dark:text-red-100">{{ stats.errors }}</p>
            </div>
            <div class="p-3 bg-red-200 dark:bg-red-800 rounded-full">
              <AlertCircle class="h-6 w-6 text-red-700 dark:text-red-300" />
            </div>
          </div>
          <div class="mt-3 text-xs text-red-600 dark:text-red-400">
            Need attention
          </div>
        </div>

        <div class="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950 dark:to-amber-900 rounded-lg border border-amber-200 dark:border-amber-800 p-6 transition-all duration-200 hover:shadow-md">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-amber-700 dark:text-amber-300 mb-1">Duplicates</p>
              <p class="text-3xl font-bold text-amber-900 dark:text-amber-100">{{ stats.duplicates }}</p>
            </div>
            <div class="p-3 bg-amber-200 dark:bg-amber-800 rounded-full">
              <Users class="h-6 w-6 text-amber-700 dark:text-amber-300" />
            </div>
          </div>
          <div class="mt-3 text-xs text-amber-600 dark:text-amber-400">
            Will be updated
          </div>
        </div>
      </div>
      
      <!-- Enhanced Filters -->
      <div v-if="currentStep !== 7" class="bg-card rounded-lg border p-6">
        <div class="flex items-center gap-3 mb-4">
          <div class="p-2 bg-muted rounded-lg">
            <Eye class="h-5 w-5 text-muted-foreground" />
          </div>
          <div>
            <h3 class="font-medium">Filter & Search</h3>
            <p class="text-sm text-muted-foreground">Find specific contacts or filter by status</p>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div class="flex-1">
            <div class="relative">
              <Eye class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search by name, email, or phone..."
                class="pl-10 max-w-md"
                v-model="filters.search"
              />
            </div>
          </div>
          <div class="flex gap-3">
            <Select v-model="filters.show_errors">
              <SelectTrigger class="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Records</SelectItem>
                <SelectItem value="new_only">New Only</SelectItem>
                <SelectItem value="duplicates_only">Duplicates Only</SelectItem>
                <SelectItem value="valid_only">Valid Only</SelectItem>
                <SelectItem value="errors_only">Errors Only</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" @click="resetFilters" class="whitespace-nowrap">
              Reset Filters
            </Button>
          </div>
        </div>
      </div>
      
      <!-- Data Table -->
      <DataTable v-if="currentStep !== 7" v-model:sort="sort" :pagination="{
        currentPage: imports.current_page,
        lastPage: imports.last_page,
        perPage: filters.per_page,
        total: imports.total,
        itemLabel: 'contact',
      }" :is-loading="isLoading" @page-change="goToPage" @update:per-page="updatePerPage" @update:sort="sort = $event">
        <template #default="{ sort, onSort }">
            <div class="overflow-x-auto w-full">
              <UITable class="min-w-[800px]">
              <TableHeader>
                <TableRow>
                  <DataTableHead :sort="sort" field="status" @sort="onSort" class="min-w-[100px]">Status</DataTableHead>
                  <DataTableHead :sort="sort" field="first_name" @sort="onSort" class="min-w-[150px]">Name</DataTableHead>
                  <DataTableHead :sort="sort" field="unit_number" @sort="onSort" class="min-w-[80px]">Unit</DataTableHead>
                  <DataTableHead :sort="sort" field="mobile_phone" @sort="onSort" class="min-w-[140px]">Phone</DataTableHead>
                  <DataTableHead :sort="sort" field="email" @sort="onSort" class="min-w-[140px]">Email</DataTableHead>
                  <TableHead class="min-w-[120px]">Contact Methods</TableHead>
                  <TableHead class="min-w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="!imports.data.length">
                  <TableCell :colspan="7" class="h-24 text-center">
                    No contacts found.
                  </TableCell>
                </TableRow>
                <TableRow v-for="contact in imports.data" :key="contact.id">
                  <TableCell class="min-w-[100px]">
                    <div class="flex items-center gap-2">
                      <Badge
                        :variant="contact.has_errors ? 'destructive' :
                                (contact.duplicate_action === 'skip' || contact.existing_contact_id) ? 'secondary' : 'default'"
                        class="text-xs"
                      >
                        {{ contact.has_errors ? 'Error' :
                           contact.duplicate_action === 'skip' ? 'Skip' :
                           contact.existing_contact_id ? 'Duplicate' : 'New' }}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell class="min-w-[150px]">
                    <div>
                      <div class="font-medium" :title="getFullName(contact)">{{ getFullName(contact) }}</div>
                      <div v-if="contact.existing_contact_id" class="text-xs text-muted-foreground">
                        Will update: {{ contact.existing_contact ? getContactFullName(contact.existing_contact) : 'Unknown' }}
                      </div>
                      <div v-else-if="contact.duplicate_action === 'skip' && contact.duplicate_reason" class="text-xs text-muted-foreground">
                        {{ contact.duplicate_reason }}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell class="min-w-[80px]">{{ contact.unit_number }}</TableCell>
                  <TableCell class="min-w-[140px]">
                    <div class="space-y-1">
                      <div v-if="contact.mobile_phone" class="text-sm" :title="contact.mobile_phone">{{ contact.mobile_phone }}</div>
                      <div v-if="contact.secondary_mobile_phone" class="text-xs text-muted-foreground" :title="contact.secondary_mobile_phone">
                        {{ contact.secondary_mobile_phone }}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell class="min-w-[140px]">
                    <div class="space-y-1">
                      <div v-if="contact.email" class="text-sm" :title="contact.email">{{ contact.email }}</div>
                      <div v-if="contact.secondary_email" class="text-xs text-muted-foreground" :title="contact.secondary_email">
                        {{ contact.secondary_email }}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell class="min-w-[120px]">
                    <div class="flex gap-1 flex-wrap">
                      <Badge v-if="contact.contact_sms" class="text-xs bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 border-blue-200 dark:border-blue-800">SMS</Badge>
                      <Badge v-if="contact.contact_email" class="text-xs bg-amber-100 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400 border-amber-200 dark:border-amber-800">Email</Badge>
                      <Badge v-if="contact.contact_wa" class="text-xs bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800">WhatsApp</Badge>
                    </div>
                  </TableCell>
                  <TableCell class="min-w-[100px]">
                    <div class="flex gap-1">
                      <ActionButton
                        v-if="contact.has_errors"
                        tooltip="View Errors"
                        variant="outline"
                        @click="showErrors(contact)"
                      >
                        <AlertCircle class="h-4 w-4" />
                      </ActionButton>
                      <ActionButton
                        tooltip="View Raw Data"
                        variant="outline"
                        @click="showRawData(contact)"
                      >
                        <Eye class="h-4 w-4" />
                      </ActionButton>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
              </UITable>
            </div>
        </template>
      </DataTable>

      <!-- Bottom Action Buttons -->
      <div v-if="currentStep !== 7" class="flex justify-between items-center pt-6 border-t bg-card rounded-lg border p-4 mt-6">
        <div class="text-sm font-medium">
          Ready to import {{ stats.valid }} valid contacts
        </div>
        <div class="flex gap-2">
          <Button variant="outline" @click="cancelImport">
            Cancel Import
          </Button>
          <Button @click="showConfirmDialog = true" class="border">
            Start Import
          </Button>
        </div>
      </div>

    <!-- Step 7: Import Complete -->
    <div v-if="currentStep === 7" class="bg-card rounded-lg border p-6">
      <div v-if="isImporting" class="text-center py-8">
        <div class="h-8 w-8 mx-auto mb-4">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        <h3 class="text-lg font-semibold mb-2">Importing Contacts</h3>
        <p class="text-muted-foreground">
          Please wait while we import your contacts...
        </p>
      </div>

      <div v-else-if="importResults" class="text-center py-12">
        <div class="h-24 w-24 mx-auto mb-8 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 rounded-full flex items-center justify-center shadow-lg">
          <Check class="h-12 w-12 text-green-600 dark:text-green-400" />
        </div>
        <h3 class="text-3xl font-bold mb-4 text-green-800 dark:text-green-200">Import Complete!</h3>
        <p class="text-lg mb-8 max-w-md mx-auto">
          Your contacts have been successfully imported and are now available in your database.
        </p>

        <!-- Enhanced Import Statistics -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 max-w-4xl mx-auto">
          <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-lg border border-green-200 dark:border-green-800 p-6 text-center transition-all duration-200 hover:shadow-md">
            <div class="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">{{ importResults.imported || 0 }}</div>
            <div class="text-sm font-medium text-green-700 dark:text-green-300">New Contacts</div>
            <div class="text-xs text-green-600 dark:text-green-400 mt-1">Successfully added</div>
          </div>
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg border border-blue-200 dark:border-blue-800 p-6 text-center transition-all duration-200 hover:shadow-md">
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">{{ importResults.updated || 0 }}</div>
            <div class="text-sm font-medium text-blue-700 dark:text-blue-300">Updated Contacts</div>
            <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Information refreshed</div>
          </div>
          <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900 rounded-lg border border-red-200 dark:border-red-800 p-6 text-center transition-all duration-200 hover:shadow-md">
            <div class="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">{{ importResults.failed || 0 }}</div>
            <div class="text-sm font-medium text-red-700 dark:text-red-300">Failed</div>
            <div class="text-xs text-red-600 dark:text-red-400 mt-1">Could not process</div>
          </div>
          <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 p-6 text-center transition-all duration-200 hover:shadow-md">
            <div class="text-3xl font-bold text-gray-600 dark:text-gray-400 mb-2">{{ importResults.skipped || 0 }}</div>
            <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Skipped</div>
            <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">Already existed</div>
          </div>
        </div>

        <!-- Enhanced Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="outline" size="lg" @click="router.visit('/tenants')" class="border-green-300 text-green-700 hover:bg-green-50 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-950">
            <Users class="mr-2 h-5 w-5" />
            View All Tenants
          </Button>
          <Button size="lg" @click="router.visit('/tenant-import')" class="bg-green-600 hover:bg-green-700 text-white">
            <Upload class="mr-2 h-5 w-5" />
            Import More Tenants
          </Button>
        </div>
      </div>
    </div>
    </div>
    <!-- Confirmation Dialog -->
    <AlertDialog v-model:open="showConfirmDialog">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Confirm Import</AlertDialogTitle>
          <AlertDialogDescription class="text-foreground">
            This will import {{ stats.valid }} valid contacts to your database.
            {{ stats.duplicates > 0 ? `${stats.duplicates} existing contacts will be updated.` : '' }}
            This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction @click="executeImport" :disabled="isImporting">
            <div v-if="isImporting" class="mr-2 h-4 w-4">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground"></div>
            </div>
            {{ isImporting ? 'Importing...' : 'Import Contacts' }}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
    
    <!-- Error Details Dialog -->
    <Dialog v-model:open="showErrorDialog">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Validation Errors</DialogTitle>
          <DialogDescription>
            The following errors were found for this contact:
          </DialogDescription>
        </DialogHeader>
        <div v-if="selectedContact" class="space-y-4">
          <div class="bg-muted rounded-lg p-4">
            <h4 class="font-medium mb-2">Contact: {{ getFullName(selectedContact) }}</h4>
            <p class="text-sm text-muted-foreground">Unit: {{ selectedContact.unit_number }}</p>
          </div>
          <div class="space-y-2">
            <div
              v-for="(error, index) in selectedContact.validation_errors"
              :key="index"
              class="flex items-start gap-2 p-3 bg-destructive/10 rounded-lg"
            >
              <AlertCircle class="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
              <span class="text-sm text-destructive">{{ error }}</span>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showErrorDialog = false">Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    
    <!-- Raw Data Dialog -->
    <Dialog v-model:open="showRawDataDialog">
      <DialogContent class="max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Raw Excel Data</DialogTitle>
          <DialogDescription>
            Original data from the Excel file for this contact:
          </DialogDescription>
        </DialogHeader>
        <div v-if="selectedContact" class="space-y-4 flex-1">
          <div class="bg-muted rounded-lg p-4">
            <h4 class="font-medium mb-2">Contact: {{ getFullName(selectedContact) }}</h4>
          </div>
          <ScrollArea orientation="both" class="border rounded-lg h-[400px]" >
            <table class="text-sm ">
              <thead class="sticky top-0 bg-muted">
                <tr class="border-b">
                  <th class="text-left p-2 font-medium min-w-[150px]">Field</th>
                  <th class="text-left p-2 font-medium min-w-[200px]">Value</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(value, key) in selectedContact.raw_data"
                  :key="key"
                  class="border-b"
                >
                  <td class="p-2 font-medium">{{ key }}</td>
                  <td class="p-2 break-words">{{ value || '(empty)' }}</td>
                </tr>
              </tbody>
            </table>
            <ScrollBar orientation="vertical" />

          </ScrollArea>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showRawDataDialog = false">Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Head, router } from '@inertiajs/vue3';
import { PageSize } from '@/types/pagination';
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import DataTable from '@/components/ui/data-table/DataTable.vue';
import DataTableHead from '@/components/ui/data-table/DataTableHead.vue';
import { Table as UITable, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import ActionButton from '@/components/ui/ActionButton.vue';
import Spinner from '@/components/ui/spinner.vue';
import ImportStepProgress from '@/components/ImportStepProgress.vue';

import { AlertCircle, Eye, Users, Check, Upload } from 'lucide-vue-next';
import debounce from 'lodash/debounce';
import type { BreadcrumbItem } from '@/types';
import type { ContactImport, ImportStats, ImportFilters } from '@/types/contact-import';
import type { SortOptions } from '@/types/contact';

interface Props {
  imports: {
    data: ContactImport[];
    current_page: number;
    last_page: number;
    total: number;
  };
  stats: ImportStats;
  sessionId: string;
  filters: ImportFilters;
}

const props = defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
  { title: 'Import', href: '/tenant-import' },
  { title: 'Preview', href: '#' },
];

// Steps for progress indicator
const steps = [
  'Select Property',
  'Upload File',
  'Map Columns',
  'Confirm',
  'Process',
  'Preview',
  'Complete'
];

const currentStep = ref(6); // Preview is step 6, will update to 7 on completion

const isLoading = ref(false);
const showConfirmDialog = ref(false);
const showErrorDialog = ref(false);
const showRawDataDialog = ref(false);
const selectedContact = ref<ContactImport | null>(null);
const isImporting = ref(false);
const importResults = ref<any>(null);

const sort = ref<SortOptions>({
  field: props.filters.sort_field || 'first_name',
  direction: (props.filters.sort_direction as 'asc' | 'desc') || 'asc'
});
const filters = ref<ImportFilters>({
  ...props.filters,
  per_page: props.filters.per_page || PageSize.Small
});

const debouncedUpdateFilters = debounce((params = {}) => {
  updateFilters(params);
}, 300);

const resetFilters = () => {
  filters.value.search = '';
  filters.value.show_errors = 'all';
  updateFilters({ page: 1 });
};



// Helper function to get full name
const getFullName = (contact: ContactImport): string => {
  return `${contact.first_name} ${contact.last_name || ''}`.trim();
};

// Helper function to get full name for Contact type
const getContactFullName = (contact: any): string => {
  return `${contact.first_name} ${contact.last_name || ''}`.trim();
};

const updateFilters = (params = {}) => {
  // Check if sessionId exists
  if (!props.sessionId) {
    console.error('Session ID is missing from props');
    alert('Session expired. Please start the import process again.');
    router.visit('/tenant-import');
    return;
  }

  // Prepare request parameters
  const requestParams = {
    import_session: props.sessionId,
    search: filters.value.search || '',
    show_errors: filters.value.show_errors || 'all',
    per_page: filters.value.per_page || 10,
    sort_field: sort.value.field || 'first_name',
    sort_direction: sort.value.direction || 'asc',
    ...params
  };

  isLoading.value = true;
  router.get('/tenant-import/preview', requestParams, {
    preserveState: true,
    preserveScroll: true,
    replace: true,
    only: ['imports', 'filters'],
    onFinish: () => {
      isLoading.value = false;
    },
    onError: (errors: any) => {
      isLoading.value = false;
      console.error('Request failed:', errors);
    }
  });
};

const goToPage = (page: number) => {
  updateFilters({ page });
};



const updatePerPage = (perPage: PageSize) => {
  filters.value.per_page = perPage;
  updateFilters({ page: 1 });
};

const showErrors = (contact: ContactImport) => {
  selectedContact.value = contact;
  showErrorDialog.value = true;
};

const showRawData = (contact: ContactImport) => {
  selectedContact.value = contact;
  showRawDataDialog.value = true;
};

const executeImport = async () => {
  isImporting.value = true;
  
  try {
    const response = await fetch('/tenant-import/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        'X-Requested-With': 'XMLHttpRequest',
      },
      body: JSON.stringify({
        session_id: props.sessionId,
      }),
    });
    
    const data = await response.json();
    
    if (data.success) {
      // Update to step 7 (Complete) and show results
      console.log('Import successful, updating to step 7');
      currentStep.value = 7;
      showConfirmDialog.value = false;
      // Store import results for the completion page
      importResults.value = data.stats;
      console.log('Current step is now:', currentStep.value);
    } else {
      alert(data.message || 'Import failed');
    }
  } catch {
    alert('Import failed. Please try again.');
  } finally {
    isImporting.value = false;
    showConfirmDialog.value = false;
  }
};

const cancelImport = async () => {
  if (confirm('Are you sure you want to cancel this import? All processed data will be lost.')) {
    try {
      await fetch('/tenant-import/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({
          session_id: props.sessionId,
        }),
      });
      
      router.visit('/tenant-import');
    } catch {
      alert('Failed to cancel import. Please try again.');
    }
  }
};

// Watch for filter changes and reload data
watch(() => filters.value.search, () => {
  debouncedUpdateFilters();
});

watch(() => filters.value.show_errors, () => {
  updateFilters({ page: 1 });
});

// Watch for sort changes (same as contact index)
watch(() => sort.value, () => {
  updateFilters();
}, { deep: true });

// Debug watcher for currentStep
watch(() => currentStep.value, (newStep, oldStep) => {
  console.log('Current step changed from', oldStep, 'to', newStep);
});
</script>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename contacts table to tenants
        Schema::rename('contacts', 'tenants');

        // Rename contact_groups table to tenant_groups and update foreign key
        Schema::rename('contact_groups', 'tenant_groups');

        // Update foreign key column name in tenant_groups table
        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign('contact_groups_contact_id_foreign');
            $table->renameColumn('contact_id', 'tenant_id');
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        // Update foreign key column name in message_recipients table
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign('message_recipients_contact_id_foreign');
            $table->renameColumn('contact_id', 'tenant_id');
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert foreign key column name in message_recipients table
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->dropForeign('message_recipients_tenant_id_foreign');
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });

        // Revert foreign key column name in tenant_groups table
        Schema::table('tenant_groups', function (Blueprint $table) {
            $table->dropForeign('tenant_groups_tenant_id_foreign');
            $table->renameColumn('tenant_id', 'contact_id');
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });

        // Rename tenant_groups table back to contact_groups
        Schema::rename('tenant_groups', 'contact_groups');

        // Rename tenants table back to contacts
        Schema::rename('tenants', 'contacts');
    }
};

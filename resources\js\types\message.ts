export interface Message {
  id: number;
  title: string; // Message title
  type?: 'sms' | 'email' | 'whatsapp'; // Legacy field, nullable for backward compatibility
  channels?: ('sms' | 'email' | 'whatsapp')[]; // New multi-channel support
  subject?: string;
  content?: string; // Legacy field, nullable
  sms_content?: string;
  email_content?: string;
  whatsapp_content?: string;
  status: 'draft' | 'queued' | 'sending' | 'completed' | 'paused' | 'failed' | 'cancelled';
  recipient_count: number;
  sent_count: number;
  failed_count: number;
  user_id: number;
  scheduled_at?: string;
  started_at?: string;
  completed_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;

  // Computed attributes
  progress_percentage: number;
  pending_count: number;

  // Relationships
  user?: User;
  recipients?: MessageRecipient[];
  logs?: MessageLog[];
  tenants?: Tenant[];
}

export interface MessageRecipient {
  id: number;
  message_id: number;
  tenant_id: number;
  recipient_type: string;
  recipient_value: string;
  channel?: string; // Channel for multi-channel messages (sms, email, whatsapp)
  status: 'pending' | 'sent' | 'failed' | 'delivered' | 'read';
  error_message?: string;
  external_id?: string;
  sent_at?: string;
  delivered_at?: string;
  read_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;

  // Relationships
  message?: Message;
  tenant?: Tenant;
}

export interface MessageLog {
  id: number;
  message_id: number;
  message_recipient_id?: number;
  event: string;
  description: string;
  data?: Record<string, any>;
  created_at: string;
  updated_at: string;

  // Relationships
  message?: Message;
  message_recipient?: MessageRecipient;
}

export interface MessageFilters {
  type?: 'sms' | 'email' | 'whatsapp';
  status?: 'draft' | 'queued' | 'sending' | 'completed' | 'paused' | 'failed' | 'cancelled';
  search?: string;
  date_from?: string;
  date_to?: string;
  per_page?: number;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface MessageStatusCounts {
  all: number;
  draft: number;
  queued: number;
  sending: number;
  completed: number;
  paused: number;
  failed: number;
  cancelled: number;
}

export interface MessageRecipientOption {
  type: 'tenant' | 'property';
  id: number;
  name: string;
  email?: string;
  phone?: string;
  property?: string;
  tenants_count?: number;
  contact_sms?: boolean;
  contact_email?: boolean;
  contact_wa?: boolean;
}

export interface MessageFormData {
  type: 'sms' | 'email' | 'whatsapp';
  subject?: string;
  content: string;
  recipients: MessageRecipientOption[];
  save_as_draft?: boolean;
}

export interface DeliveryStats {
  total: number;
  sent: number;
  failed: number;
  pending: number;
  progress: number;
}

export interface PaginatedMessages {
  data: Message[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

// Import types from existing files
import type { User } from '@/types/user';
import type { Tenant } from '@/types/tenant';
import type { Property } from '@/types/property';

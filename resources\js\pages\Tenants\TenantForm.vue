<script setup lang="ts">
import { computed, ref } from 'vue';
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import InputError from '@/components/InputError.vue';
import <PERSON>Alert from '@/components/FlashAlert.vue';
import { Separator } from '@/components/ui/separator';
import Combobox from '@/components/ui/combobox.vue';
import GroupSelector from '@/components/Tenants/GroupSelector.vue';
import DateTime from '@/components/ui/DateTime.vue';
import {
  User,
  Mail,
  Phone,
  MessageCircle,
  MessageSquare,
  Building,
  Hash,
  ToggleLeft,
  Settings,
  Info,
  HelpCircle,
  Users,
  Calendar
} from 'lucide-vue-next';
import type { Property } from '@/types/property';
import type { Group } from '@/types/group';

interface Props {
  tenant?: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    secondary_email: string;
    mobile_phone: string;
    secondary_mobile_phone: string;
    whatsapp_number: string;
    secondary_whatsapp_number: string;
    contact_sms: boolean;
    contact_wa: boolean;
    contact_email: boolean;
    property_id: number;
    unit_number: string;
    status: boolean;
    groups?: Group[];
    created_at?: string;
    updated_at?: string;
  };
  properties: Property[];
  groups: Group[];
}

type FormData = {
  id: number | null;
  first_name: string;
  last_name: string;
  email: string;
  secondary_email: string;
  mobile_phone: string;
  secondary_mobile_phone: string;
  whatsapp_number: string;
  secondary_whatsapp_number: string;
  contact_sms: boolean;
  contact_wa: boolean;
  contact_email: boolean;
  property_id: number | null;
  unit_number: string;
  status: boolean;
  groups: number[];
}

const props = defineProps<Props>();

const formatPhoneNumberValue = (value: string): string => {
  if (!value) return '';
  
  const cleaned = value.replace(/\D/g, '');
  
  if (cleaned.length > 0) {
    if (cleaned.length <= 10) {
      // Format: (XXX) XXX-XXXX
      return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else {
      // Format: 1 (XXX) XXX-XXXX
      return cleaned.replace(/(\d{1})(\d{3})(\d{3})(\d{4})/, '$1 ($2) $3-$4');
    }
  }
  
  return value;
};

const form = useForm<FormData>({
  id: props.tenant?.id || null,
  first_name: props.tenant?.first_name || '',
  last_name: props.tenant?.last_name || '',
  email: props.tenant?.email || '',
  secondary_email: props.tenant?.secondary_email || '',
  mobile_phone: formatPhoneNumberValue(props.tenant?.mobile_phone || ''),
  secondary_mobile_phone: formatPhoneNumberValue(props.tenant?.secondary_mobile_phone || ''),
  whatsapp_number: formatPhoneNumberValue(props.tenant?.whatsapp_number || ''),
  secondary_whatsapp_number: formatPhoneNumberValue(props.tenant?.secondary_whatsapp_number || ''),
  contact_sms: typeof props.tenant?.contact_sms !== 'undefined' ? props.tenant.contact_sms : true,
  contact_wa: typeof props.tenant?.contact_wa !== 'undefined' ? props.tenant.contact_wa : true,
  contact_email: typeof props.tenant?.contact_email !== 'undefined' ? props.tenant.contact_email : true,
  property_id: props.tenant?.property_id || null,
  unit_number: props.tenant?.unit_number || '',
  status: typeof props.tenant?.status !== 'undefined' ? props.tenant.status : true,
  groups: props.tenant?.groups?.map(group => group.id) || [],
}) as any;

// Reactive groups state (to allow adding new groups)
const groups = ref([...props.groups]);

const isEditing = computed(() => Boolean(props.contact?.id));

const propertyOptions = computed(() => {
  return props.properties.map(property => ({
    value: property.id,
    label: property.name
  }));
});

const formatPhoneNumber = (event: Event, field: keyof FormData) => {
  const input = event.target as HTMLInputElement;
  let value = input.value.replace(/\D/g, '');
  
  if (value.length > 0) {
    if (value.length <= 10) {
      // Format: (XXX) XXX-XXXX
      value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else {
      // Format: 1 (XXX) XXX-XXXX
      value = value.replace(/(\d{1})(\d{3})(\d{3})(\d{4})/, '$1 ($2) $3-$4');
    }
  }
  
  form[field] = value;
};

const breadcrumbs = [
  { title: 'Contacts', href: '/contacts' },
  { title: form.id ? 'Edit Contact' : 'Add Contact', href: form.id ? `/contacts/${form.id}/edit` : '/contacts/create' },
];

const submit = () => {
  if (form.id) {
    form.patch(route('contacts.update', { contact: form.id }), {
      onError: (errors) => {
        // Scroll to first error
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.focus();
          }
        }
      }
    });
  } else {
    form.post(route('contacts.store'), {
      onError: (errors) => {
        // Scroll to first error
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.focus();
          }
        }
      }
    });
  }
};

// Group selection methods
const updateGroups = (groupIds: number[]) => {
  form.groups = groupIds;
};

const handleGroupCreated = (newGroup: any) => {
  // Add the new group to the reactive groups list
  groups.value.push(newGroup);
};

// Debug: Log the form values to see what's happening
console.log('Contact form initialization:', {
  contact_sms: form.contact_sms,
  contact_wa: form.contact_wa,
  contact_email: form.contact_email,
  props_contact: props.contact
});


</script>

<template>
  <Head :title="form.id ? 'Edit Contact' : 'Add Contact'" />
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <Heading
        :title="form.id ? 'Edit Contact' : 'Add Contact'"
        :description="form.id ? 'Update the contact information below.' : 'Fill in the contact details to add a new contact to the system.'"
      />
      <Separator class="-mt-5" />
      <FlashAlert :errors="form.errors" />

      <!-- Form Section -->
      <div class="grid gap-8 lg:grid-cols-3 max-w-7xl">
        <!-- Main Form -->
        <div class="lg:col-span-2">
          <Card class="card-primary">
            <CardHeader class="card-header-primary">
              <CardTitle class="card-title-primary">
                <User class="card-title-icon" />
                Contact Information
              </CardTitle>
              <CardDescription>
                {{ isEditing ? 'Update the contact\'s information below.' : 'Enter the contact details for the new contact.' }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form @submit.prevent="submit" class="form-section">
                <!-- Name Fields -->
                <div class="grid gap-4 sm:grid-cols-2">
                  <div class="form-field">
                    <Label for="first_name" class="form-label">
                      <User class="form-label-icon" />
                      First Name
                    </Label>
                    <Input
                      id="first_name"
                      v-model="form.first_name"
                      required
                      placeholder="e.g., John, Sarah, Michael"
                      autocomplete="given-name"
                      class="form-input"
                      :class="{
                        'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.first_name,
                        'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.first_name && !form.errors.first_name
                      }"
                      @input="form.clearErrors('first_name')"
                    />
                    <InputError :message="form.errors.first_name" />
                  </div>
                  <div class="form-field">
                    <Label for="last_name" class="form-label">
                      <User class="form-label-icon" />
                      Last Name
                    </Label>
                    <Input
                      id="last_name"
                      v-model="form.last_name"
                      placeholder="e.g., Smith, Johnson, Williams"
                      autocomplete="family-name"
                      class="form-input"
                      :class="{
                        'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.last_name,
                        'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.last_name && !form.errors.last_name
                      }"
                      @input="form.clearErrors('last_name')"
                    />
                    <InputError :message="form.errors.last_name" />
                  </div>
                </div>

                <!-- Contact Information -->
                <div class="grid gap-6">
                  <!-- Primary and Secondary Email -->
                  <div class="grid gap-4 sm:grid-cols-2">
                    <div class="space-y-4">
                      <div class="form-field">
                        <Label for="email" class="form-label">
                          <Mail class="form-label-icon" />
                          Primary Email
                        </Label>
                        <Input
                          id="email"
                          v-model="form.email"
                          type="email"
                          placeholder="<EMAIL>"
                          autocomplete="email"
                          class="form-input"
                          :class="{
                            'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.email,
                            'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.email && !form.errors.email
                          }"
                          @input="form.clearErrors('email')"
                        />
                        <InputError :message="form.errors.email" />
                      </div>
                      <div class="form-field">
                        <Label for="mobile_phone" class="form-label">
                          <Phone class="form-label-icon" />
                          Primary Phone
                        </Label>
                        <Input
                          id="mobile_phone"
                          v-model="form.mobile_phone"
                          placeholder="(*************"
                          autocomplete="tel"
                          class="form-input"
                          :class="{
                            'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.mobile_phone,
                            'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.mobile_phone && !form.errors.mobile_phone
                          }"
                          @input="formatPhoneNumber($event, 'mobile_phone')"
                        />
                        <InputError :message="form.errors.mobile_phone" />
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="form-field">
                        <Label for="secondary_email" class="form-label">
                          <Mail class="form-label-icon" />
                          Secondary Email
                        </Label>
                        <Input
                          id="secondary_email"
                          v-model="form.secondary_email"
                          type="email"
                          placeholder="<EMAIL>"
                          class="form-input"
                          :class="{
                            'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.secondary_email,
                            'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.secondary_email && !form.errors.secondary_email
                          }"
                          @input="form.clearErrors('secondary_email')"
                        />
                        <InputError :message="form.errors.secondary_email" />
                      </div>
                      <div class="form-field">
                        <Label for="secondary_mobile_phone" class="form-label">
                          <Phone class="form-label-icon" />
                          Secondary Phone
                        </Label>
                        <Input
                          id="secondary_mobile_phone"
                          v-model="form.secondary_mobile_phone"
                          placeholder="(*************"
                          class="form-input"
                          :class="{
                            'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.secondary_mobile_phone,
                            'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.secondary_mobile_phone && !form.errors.secondary_mobile_phone
                          }"
                          @input="formatPhoneNumber($event, 'secondary_mobile_phone')"
                        />
                        <InputError :message="form.errors.secondary_mobile_phone" />
                      </div>
                    </div>
                  </div>

                  <!-- WhatsApp Numbers -->
                  <div class="grid gap-4 sm:grid-cols-2">
                    <div class="form-field">
                      <Label for="whatsapp_number" class="form-label">
                        <MessageCircle class="form-label-icon" />
                        WhatsApp Number
                      </Label>
                      <Input
                        id="whatsapp_number"
                        v-model="form.whatsapp_number"
                        placeholder="(*************"
                        class="form-input"
                        :class="{
                          'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.whatsapp_number,
                          'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.whatsapp_number && !form.errors.whatsapp_number
                        }"
                        @input="formatPhoneNumber($event, 'whatsapp_number')"
                      />
                      <InputError :message="form.errors.whatsapp_number" />
                    </div>
                    <div class="form-field">
                      <Label for="secondary_whatsapp_number" class="form-label">
                        <MessageCircle class="form-label-icon" />
                        Secondary WhatsApp
                      </Label>
                      <Input
                        id="secondary_whatsapp_number"
                        v-model="form.secondary_whatsapp_number"
                        placeholder="(*************"
                        class="form-input"
                        :class="{
                          'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.secondary_whatsapp_number,
                          'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.secondary_whatsapp_number && !form.errors.secondary_whatsapp_number
                        }"
                        @input="formatPhoneNumber($event, 'secondary_whatsapp_number')"
                      />
                      <InputError :message="form.errors.secondary_whatsapp_number" />
                    </div>
                  </div>
                </div>

                <!-- Contact Methods -->
                <div class="form-field">
                  <Label class="form-label">
                    <Settings class="form-label-icon" />
                    Contact Methods
                  </Label>
                  <div class="grid gap-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 p-4">
                    <div class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900/50 hover:border-gray-300 dark:hover:border-gray-600 transition-colors">
                      <Checkbox
                        id="contact_sms"
                        v-model="form.contact_sms"
                        class="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                      />
                      <div class="flex items-center gap-2 flex-1">
                        <MessageSquare class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <Label for="contact_sms" class="cursor-pointer font-medium">Contact via SMS</Label>
                      </div>
                    </div>
                    <div class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900/50 hover:border-gray-300 dark:hover:border-gray-600 transition-colors">
                      <Checkbox
                        id="contact_wa"
                        v-model="form.contact_wa"
                        class="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                      />
                      <div class="flex items-center gap-2 flex-1">
                        <MessageCircle class="h-4 w-4 text-green-600 dark:text-green-400" />
                        <Label for="contact_wa" class="cursor-pointer font-medium">Contact via WhatsApp</Label>
                      </div>
                    </div>
                    <div class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900/50 hover:border-gray-300 dark:hover:border-gray-600 transition-colors">
                      <Checkbox
                        id="contact_email"
                        v-model="form.contact_email"
                        class="data-[state=checked]:bg-amber-600 data-[state=checked]:border-amber-600"
                      />
                      <div class="flex items-center gap-2 flex-1">
                        <Mail class="h-4 w-4 text-amber-600 dark:text-amber-400" />
                        <Label for="contact_email" class="cursor-pointer font-medium">Contact via Email</Label>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-start gap-2 text-xs text-muted-foreground">
                    <Settings class="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p>Select the communication methods this contact prefers.</p>
                      <p class="mt-1">You can choose multiple options based on their preferences.</p>
                    </div>
                  </div>
                  <InputError :message="form.errors.contact_sms || form.errors.contact_wa || form.errors.contact_email" />
                </div>

                <!-- Property Selection -->
                <div class="form-field">
                  <Label for="property" class="form-label">
                    <Building class="form-label-icon" />
                    Property
                  </Label>
                  <Combobox
                    v-model="form.property_id"
                    :items="propertyOptions"
                    placeholder="Select a property..."
                    search-placeholder="Search properties..."
                    empty-text="No properties found."
                    clearable
                    class="form-input"
                  />
                  <div class="flex items-start gap-2 text-xs text-muted-foreground">
                    <Building class="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p>Choose the property this contact is associated with.</p>
                      <p class="mt-1">This helps organize contacts by location and building.</p>
                    </div>
                  </div>
                  <InputError :message="form.errors.property_id" />
                </div>

                <!-- Groups Selection -->
                <div class="form-field">
                  <Label for="groups" class="form-label">
                    <Users class="form-label-icon" />
                    Groups
                  </Label>
                  <GroupSelector
                    v-model="form.groups"
                    :groups="groups"
                    :error="form.errors.groups"
                    @update:modelValue="updateGroups"
                    @groupCreated="handleGroupCreated"
                  />
                </div>

                <!-- Unit Number -->
                <div class="form-field">
                  <Label for="unit_number" class="form-label">
                    <Hash class="form-label-icon" />
                    Unit Number
                  </Label>
                  <Input
                    id="unit_number"
                    v-model="form.unit_number"
                    placeholder="e.g., 101, A-205, Unit 15"
                    class="form-input"
                    :class="{
                      'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.unit_number,
                      'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.unit_number && !form.errors.unit_number
                    }"
                    @input="form.clearErrors('unit_number')"
                  />
                  <div class="flex items-start gap-2 text-xs text-muted-foreground">
                    <Hash class="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p>Optional unit or apartment number within the property.</p>
                      <p class="mt-1">Leave blank if not applicable or unknown.</p>
                    </div>
                  </div>
                  <InputError :message="form.errors.unit_number" />
                </div>

                <!-- Status -->
                <div class="form-field">
                  <Label for="status" class="form-label">
                    <ToggleLeft class="form-label-icon" />
                    Contact Status
                  </Label>
                  <div class="flex items-center gap-3 p-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
                    <Switch
                      id="status"
                      v-model="form.status"
                      :class="form.status ? 'data-[state=checked]:bg-green-600' : 'data-[state=unchecked]:bg-gray-400'"
                    />
                    <div class="flex flex-col">
                      <span class="font-medium" :class="form.status ? 'text-green-700 dark:text-green-400' : 'text-gray-700 dark:text-gray-400'">
                        {{ form.status ? 'Active' : 'Inactive' }}
                      </span>
                      <span class="text-xs text-muted-foreground">
                        {{ form.status ? 'Contact can receive messages' : 'Contact will not receive messages' }}
                      </span>
                    </div>
                  </div>
                  <InputError :message="form.errors.status" />
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                  <div class="flex flex-col sm:flex-row gap-3">
                    <Button
                      type="submit"
                      :disabled="form.processing || !form.first_name"
                      class="form-submit-button flex-1 sm:flex-none"
                      size="lg"
                    >
                      <span v-if="form.processing" class="flex items-center gap-2">
                        <div class="h-4 w-4 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
                        {{ isEditing ? 'Updating Contact...' : 'Creating Contact...' }}
                      </span>
                      <span v-else class="flex items-center gap-2">
                        <User class="h-4 w-4" />
                        {{ isEditing ? 'Update Contact' : 'Create Contact' }}
                      </span>
                    </Button>
                  </div>

                  <!-- Form Status Feedback -->
                  <div v-if="form.first_name && !form.errors.first_name" class="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                    <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span>Contact name looks good!</span>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Details Card -->
          <Card class="sidebar-card-details">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Info class="h-4 w-4" />
                Details
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content">
              <div class="space-y-3">
                <!-- Contact ID -->
                <div v-if="isEditing" class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Hash class="h-4 w-4" />
                    Contact ID
                  </span>
                  <span class="text-sm font-medium">#{{ form.id }}</span>
                </div>

                <!-- Created -->
                <div v-if="isEditing && props.contact?.created_at" class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Created
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="props.contact.created_at" type="absolute" /></span>
                </div>

                <!-- Status -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <ToggleLeft class="h-4 w-4" />
                    Status
                  </span>
                  <span class="text-sm font-medium">
                    {{ isEditing ? 'Editing existing contact' : 'Creating new contact' }}
                  </span>
                </div>

                <!-- Property -->
                <div v-if="form.property_id" class="flex items-center justify-between py-2">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Building class="h-4 w-4" />
                    Property
                  </span>
                  <span class="text-sm font-medium">
                    {{ propertyOptions.find(p => p.value === form.property_id)?.label || 'Unknown' }}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Help Card -->
          <Card class="sidebar-card-help">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title-help">
                <HelpCircle class="h-4 w-4" />
                Need Help?
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content-help">
              <div class="space-y-4 text-sm">
                <div>
                  <h4 class="font-semibold mb-2">Contact Information Guidelines</h4>
                  <ul class="space-y-1 list-disc list-inside">
                    <li>First name is required for all contacts</li>
                    <li>Provide at least one contact method (email, phone, or WhatsApp)</li>
                    <li>Phone numbers are automatically formatted</li>
                    <li>Unit numbers are optional but helpful for organization</li>
                  </ul>
                </div>

                <div>
                  <h4 class="font-semibold mb-2">Contact Methods</h4>
                  <ul class="space-y-1 text-xs">
                    <li>• <strong>SMS:</strong> Text messages to mobile phone</li>
                    <li>• <strong>WhatsApp:</strong> Messages via WhatsApp</li>
                    <li>• <strong>Email:</strong> Email notifications and updates</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
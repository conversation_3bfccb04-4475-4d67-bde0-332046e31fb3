<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename contact_imports table to tenant_imports
        Schema::rename('contact_imports', 'tenant_imports');

        // Update the foreign key column name from existing_contact_id to existing_tenant_id
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->renameColumn('existing_contact_id', 'existing_tenant_id');
        });

        // Update the foreign key constraint to reference tenants table
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign('contact_imports_existing_contact_id_foreign');
            $table->foreign('existing_tenant_id')->references('id')->on('tenants')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the foreign key constraint
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->dropForeign('tenant_imports_existing_tenant_id_foreign');
            $table->foreign('existing_tenant_id')->references('id')->on('contacts')->onDelete('set null');
        });

        // Rename the column back
        Schema::table('tenant_imports', function (Blueprint $table) {
            $table->renameColumn('existing_tenant_id', 'existing_contact_id');
        });

        // Rename table back
        Schema::rename('tenant_imports', 'contact_imports');
    }
};

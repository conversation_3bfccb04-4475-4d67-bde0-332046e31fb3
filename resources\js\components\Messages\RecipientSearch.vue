<template>
  <div class="relative" style="z-index: 50">
    <!-- Tabs Container -->
    <div class="relative" style="z-index: 40">
      <Tabs v-model="selectedRecipientType" class="w-full">
        <TabsList class="grid w-full grid-cols-3">
          <TabsTrigger value="tenants" class="flex items-center gap-2 cursor-pointer">
            <User class="h-4 w-4" />
            Tenants
          </TabsTrigger>
          <TabsTrigger value="properties" class="flex items-center gap-2 cursor-pointer">
            <Building2 class="h-4 w-4" />
            Properties
          </TabsTrigger>
          <TabsTrigger value="groups" class="flex items-center gap-2 cursor-pointer">
            <Users class="h-4 w-4" />
            Groups
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>

    <!-- Search and Dropdown Container -->
    <div class="relative" ref="dropdownRef">
      <!-- Search Input -->
      <div class="relative mt-2" style="z-index: 40">
        <Search class="absolute left-3 top-3 h-4 w-4 text-gray-400" />
        <Input
          v-model="recipientSearch"
          type="text"
          :placeholder="selectedRecipientType === 'tenants'
            ? 'Search tenants by name, email, or phone...'
            : selectedRecipientType === 'properties'
            ? 'Search properties by name...'
            : 'Search groups by name...'"
          class="pl-10"
          autocomplete="new-password"
          @input="searchRecipients"
          @focus="showDropdown = true"
        />
      </div>

      <!-- Dropdown Results -->
      <div 
        v-if="showDropdown"
        class="absolute top-[calc(100%+0.25rem)] left-0 right-0 bg-background border rounded-md shadow-lg overflow-hidden"
        style="z-index: 999"
      >
        <div class="max-h-[300px] overflow-y-auto">
          <div v-if="isLoadingRecipients" class="h-full flex items-center justify-center">
            <div class="text-center">
              <LoaderCircle class="h-5 w-5 animate-spin mx-auto" />
              <span class="text-sm text-muted-foreground mt-2 block">Loading...</span>
            </div>
          </div>
          <div v-else>
            <!-- Search Results -->
            <div v-if="currentResults?.length > 0">
              <div 
                v-for="result in currentResults" 
                :key="result?.id"
                @click="addRecipient(result)"
                class="p-3 hover:bg-muted flex items-center justify-between cursor-pointer group border-b last:border-b-0"
              >
                <div class="flex items-center gap-3">
                  <component
                    :is="selectedRecipientType === 'contacts' ? User : selectedRecipientType === 'properties' ? Building2 : Users"
                    class="h-4 w-4 text-muted-foreground flex-shrink-0"
                  />
                  <div class="grid gap-0.5 min-w-0">
                    <span class="text-sm font-medium truncate">
                      {{ result?.display_name || 'Unnamed' }}
                    </span>
                    <span v-if="selectedRecipientType === 'contacts'" class="text-xs text-muted-foreground truncate">
                      {{ result?.email || result?.mobile_phone || 'No contact info' }}
                    </span>
                    <span v-else class="text-xs text-muted-foreground">
                      {{ result?.contacts_count || 0 }} {{ (result?.contacts_count || 0) === 1 ? 'contact' : 'contacts' }}
                    </span>
                  </div>
                </div>
                <Button 
                  type="button"
                  variant="ghost"
                  size="sm"
                  class="opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
                >
                  Add
                </Button>
              </div>
            </div>
            <!-- Empty Search Results -->
            <div 
              v-else-if="recipientSearch" 
              class="h-full flex items-center justify-center"
            >
              <div class="text-center p-4">
                <component 
                  :is="selectedRecipientType === 'contacts' ? User : Building2" 
                  class="h-8 w-8 mx-auto mb-2 text-muted-foreground/50"
                />
                <p class="text-sm text-muted-foreground">No {{ selectedRecipientType }} found</p>
              </div>
            </div>
            <!-- Initial/Empty State -->
            <div v-else class="h-full">
              <div class="p-4 text-center border-b">
                <component 
                  :is="selectedRecipientType === 'contacts' ? User : Building2" 
                  class="h-8 w-8 mx-auto mb-2 text-muted-foreground/50"
                />
                <p class="text-sm text-muted-foreground">
                  {{ (currentResults?.length || 0) === 0 ? 'No ' + selectedRecipientType + ' available' : 'Search or select from list' }}
                </p>
              </div>
              
              <!-- Show initial results if available -->
              <div v-if="currentResults?.length > 0" class="py-2">
                <div 
                  v-for="result in currentResults" 
                  :key="result?.id"
                  @click="addRecipient(result)"
                  class="px-4 py-2 hover:bg-muted flex items-center justify-between cursor-pointer group"
                >
                  <div class="flex items-center gap-2 min-w-0">
                    <component
                      :is="selectedRecipientType === 'tenants' ? User : selectedRecipientType === 'properties' ? Building2 : Users"
                      class="h-4 w-4 text-muted-foreground flex-shrink-0"
                    />
                    <span class="text-sm truncate">{{ result?.display_name || 'Unnamed' }}</span>
                  </div>
                  <span v-if="selectedRecipientType === 'properties' || selectedRecipientType === 'groups'" class="text-xs text-muted-foreground">
                    {{ result?.tenants_count || 0 }} {{ (result?.tenants_count || 0) === 1 ? 'tenant' : 'tenants' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Selected Recipients Tags -->
    <SelectedRecipients
      v-if="recipients.length > 0"
      :recipients="recipients"
      :groups="props.groups"
      @remove-recipient="removeRecipient"
      @clear-all="clearAllRecipients"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Search, User, Building2, Users, LoaderCircle } from 'lucide-vue-next';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { onClickOutside } from '@vueuse/core';
import debounce from 'lodash/debounce';
import axios from 'axios';
import SelectedRecipients from './SelectedRecipients.vue';

interface Recipient {
  type: string;
  id: string;
  name: string;
}

interface Tenant {
  id: number;
  name: string;
  email: string;
  phone: string;
  property?: string;
  contact_sms: boolean;
  contact_email: boolean;
  contact_wa: boolean;
}

interface Property {
  id: number;
  name: string;
  contacts_count: number;
}

interface Group {
  id: number;
  name: string;
  color: string;
  contacts_count: number;
}

interface Props {
  recipients: Recipient[];
  messageChannels: string[];
  tenants: Tenant[];
  properties: Property[];
  groups?: Group[];
}

interface Emits {
  (e: 'update:recipients', value: Recipient[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Reactive state
const dropdownRef = ref<HTMLElement | null>(null);
const showDropdown = ref(false);
const recipientSearch = ref('');
const isLoadingRecipients = ref(false);
const selectedRecipientType = ref<'tenants' | 'properties' | 'groups'>('tenants');

// Search results
const searchResults = ref({
  tenants: [] as any[],
  properties: [] as any[],
  groups: [] as any[]
});

// Computed
const recipients = computed({
  get: () => props.recipients,
  set: (value: Recipient[]) => emit('update:recipients', value)
});

const currentResults = computed(() => {
  const type = selectedRecipientType.value;
  return searchResults.value?.[type] || [];
});

// Helper functions
const getDisplayName = (result: any, type: 'contacts' | 'properties' | 'groups') => {
  if (type === 'contacts') {
    if (result.full_name) return result.full_name;
    if (result.name) return result.name;
    if (result.first_name || result.last_name) {
      return `${result.first_name || ''} ${result.last_name || ''}`.trim();
    }
    return 'Unnamed Contact';
  }
  if (type === 'groups') {
    return result.name || 'Unnamed Group';
  }
  return result.name || 'Unnamed Property';
};

// Methods
const loadInitialRecipients = async () => {
  if (!recipientSearch.value?.trim()) {
    if (selectedRecipientType.value === 'contacts') {
      const processedContacts = props.contacts.map(contact => ({
        ...contact,
        id: contact.id?.toString() || '',
        display_name: getDisplayName(contact, 'contacts')
      }));

      const filteredContacts = processedContacts
        .filter(contact =>
          !props.recipients?.some(r => r.type === 'contact' && r.id === contact.id)
        )
        .slice(0, 10);
      searchResults.value.contacts = filteredContacts;
    } else if (selectedRecipientType.value === 'properties') {
      const processedProperties = props.properties.map(property => ({
        ...property,
        id: property.id?.toString() || '',
        display_name: property.name || 'Unnamed Property',
        contacts_count: property.contacts_count // Use the actual count from backend
      }));

      const filteredProperties = processedProperties
        .filter(property =>
          !props.recipients?.some(r => r.type === 'property' && r.id === property.id)
        )
        .slice(0, 10);
      searchResults.value.properties = filteredProperties;
    } else if (selectedRecipientType.value === 'groups') {
      const processedGroups = (props.groups || []).map(group => ({
        ...group,
        id: group.id?.toString() || '',
        display_name: group.name || 'Unnamed Group',
        contacts_count: group.contacts_count
      }));

      const filteredGroups = processedGroups
        .filter(group =>
          !props.recipients?.some(r => r.type === 'group' && r.id === group.id)
        )
        .slice(0, 10);
      searchResults.value.groups = filteredGroups;
    }
  }
};

const searchRecipients = debounce(async () => {
  if (!recipientSearch.value.trim()) {
    await loadInitialRecipients();
    return;
  }

  isLoadingRecipients.value = true;
  try {
    const response = await axios.get(route('messages.search-recipients'), {
      params: {
        search: recipientSearch.value,
        type: props.messageType,
        recipient_type: selectedRecipientType.value,
        limit: 10
      }
    });

    if (selectedRecipientType.value === 'contacts') {
      const contacts = Array.isArray(response.data.contacts) ? response.data.contacts : [];
      searchResults.value.contacts = contacts
        .filter(contact => !props.recipients.some(r => r.type === 'contact' && r.id === contact.id))
        .map((contact: any) => ({
          ...contact,
          id: contact.id?.toString() || '',
          display_name: getDisplayName(contact, 'contacts')
        }));
    } else if (selectedRecipientType.value === 'properties') {
      let properties = Array.isArray(response.data.properties) ? response.data.properties : [];
      properties = properties
        .filter(property =>
          !props.recipients?.some(r => r.type === 'property' && r.id === property.id)
        )
        .map((property: any) => ({
          ...property,
          id: property.id?.toString() || '',
          display_name: property.name || 'Unnamed Property',
          contacts_count: property.contacts_count // Use the actual count from API response
        }));
      searchResults.value.properties = properties;
    } else if (selectedRecipientType.value === 'groups') {
      let groups = Array.isArray(response.data.groups) ? response.data.groups : [];
      groups = groups
        .filter(group =>
          !props.recipients?.some(r => r.type === 'group' && r.id === group.id)
        )
        .map((group: any) => ({
          ...group,
          id: group.id?.toString() || '',
          display_name: group.name || 'Unnamed Group',
          contacts_count: group.contacts_count
        }));
      searchResults.value.groups = groups;
    }
  } catch (error) {
    console.error('Error searching recipients:', error);
    searchResults.value[selectedRecipientType.value] = [];
  } finally {
    isLoadingRecipients.value = false;
  }
}, 300);

const addRecipient = (result: any) => {
  const recipientType = selectedRecipientType.value === 'contacts' ? 'contact' :
                       selectedRecipientType.value === 'properties' ? 'property' : 'group';

  const exists = props.recipients.some(r =>
    r.type === recipientType &&
    r.id === result.id.toString()
  );

  if (!exists) {
    const newRecipients = [...props.recipients, {
      type: recipientType,
      id: result.id.toString(),
      name: result.display_name || getDisplayName(result, selectedRecipientType.value)
    }];
    emit('update:recipients', newRecipients);
  }

  recipientSearch.value = '';
  showDropdown.value = false;
};

const removeRecipient = (recipient: Recipient) => {
  const newRecipients = props.recipients.filter(r =>
    !(r.type === recipient.type && r.id === recipient.id)
  );
  emit('update:recipients', newRecipients);
};

const clearAllRecipients = () => {
  emit('update:recipients', []);
  showDropdown.value = false;
};

// Event handlers
onClickOutside(dropdownRef, () => {
  showDropdown.value = false;
});

// Watchers
watch(showDropdown, (newValue) => {
  if (newValue) {
    loadInitialRecipients();
  }
});

watch(selectedRecipientType, () => {
  loadInitialRecipients();
});

watch(() => props.recipients, () => {
  if (showDropdown.value) {
    loadInitialRecipients();
  }
}, { deep: true });

onMounted(() => {
  loadInitialRecipients();
});
</script>

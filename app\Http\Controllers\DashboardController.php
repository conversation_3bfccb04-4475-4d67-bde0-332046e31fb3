<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\Message;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Tenant statistics
        $totalTenants = Tenant::count();
        $activeTenants = Tenant::where('status', true)->count();
        $inactiveTenants = Tenant::where('status', false)->count();
        $smsEnabledTenants = Tenant::where('contact_sms', true)->count();
        $emailEnabledTenants = Tenant::where('contact_email', true)->count();
        $whatsappEnabledTenants = Tenant::where('contact_wa', true)->count();

        // Message statistics
        $totalMessages = Message::count();
        $sentMessages = Message::where('status', 'completed')->count(); // Fixed: completed messages, not 'sent'
        $failedMessages = Message::where('status', 'failed')->count();
        $pendingMessages = Message::whereIn('status', ['pending', 'queued', 'sending'])->count();
        $draftMessages = Message::where('status', 'draft')->count();

        // Recipient-based statistics for more accurate counts
        $totalRecipients = DB::table('message_recipients')->count();
        $sentRecipients = DB::table('message_recipients')->whereIn('status', ['sent', 'delivered', 'read'])->count();
        $failedRecipients = DB::table('message_recipients')->where('status', 'failed')->count();
        $pendingRecipients = DB::table('message_recipients')->where('status', 'pending')->count();
        
        // Time-based message statistics
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();
        
        $messagesToday = Message::where('created_at', '>=', $today)->count();
        $messagesThisWeek = Message::where('created_at', '>=', $thisWeek)->count();
        $messagesThisMonth = Message::where('created_at', '>=', $thisMonth)->count();

        // Property statistics (removed from dashboard but kept for potential future use)
        // $totalProperties = Property::count();
        // $propertiesWithContacts = Property::has('contacts')->count();

        // Delivery statistics by type (based on recipients, not messages)
        // Use channel field from message_recipients for accurate multi-channel stats
        $smsDeliveryStats = [
            'sent' => DB::table('message_recipients')
                ->where('channel', 'sms')
                ->where('status', 'sent')
                ->count(),
            'failed' => DB::table('message_recipients')
                ->where('channel', 'sms')
                ->where('status', 'failed')
                ->count(),
            'pending' => DB::table('message_recipients')
                ->where('channel', 'sms')
                ->whereIn('status', ['pending', 'queued', 'sending'])
                ->count(),
        ];

        $emailDeliveryStats = [
            'sent' => DB::table('message_recipients')
                ->where('channel', 'email')
                ->where('status', 'sent')
                ->count(),
            'failed' => DB::table('message_recipients')
                ->where('channel', 'email')
                ->where('status', 'failed')
                ->count(),
            'pending' => DB::table('message_recipients')
                ->where('channel', 'email')
                ->whereIn('status', ['pending', 'queued', 'sending'])
                ->count(),
        ];

        $whatsappDeliveryStats = [
            'sent' => DB::table('message_recipients')
                ->where('channel', 'whatsapp')
                ->where('status', 'sent')
                ->count(),
            'failed' => DB::table('message_recipients')
                ->where('channel', 'whatsapp')
                ->where('status', 'failed')
                ->count(),
            'pending' => DB::table('message_recipients')
                ->where('channel', 'whatsapp')
                ->whereIn('status', ['pending', 'queued', 'sending'])
                ->count(),
        ];

        // Messages by type for chart - count by channels for multi-channel support
        $messagesByType = [
            'sms' => Message::where('type', 'sms')
                ->orWhereJsonContains('channels', 'sms')
                ->count(),
            'email' => Message::where('type', 'email')
                ->orWhereJsonContains('channels', 'email')
                ->count(),
            'whatsapp' => Message::where('type', 'whatsapp')
                ->orWhereJsonContains('channels', 'whatsapp')
                ->count(),
        ];

        // Ensure we have some data for demo purposes if no messages exist
        if ($messagesByType['sms'] === 0 && $messagesByType['email'] === 0 && $messagesByType['whatsapp'] === 0) {
            $messagesByType = [
                'sms' => 0,
                'email' => 0,
                'whatsapp' => 0,
            ];
        }

        // Weekly message trends (last 7 days) - support multi-channel
        $weeklyTrends = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $weeklyTrends[] = [
                'date' => $date->format('M j'),
                'sms' => Message::where(function ($query) {
                        $query->where('type', 'sms')
                              ->orWhereJsonContains('channels', 'sms');
                    })
                    ->whereDate('created_at', $date)
                    ->count(),
                'email' => Message::where(function ($query) {
                        $query->where('type', 'email')
                              ->orWhereJsonContains('channels', 'email');
                    })
                    ->whereDate('created_at', $date)
                    ->count(),
                'whatsapp' => Message::where(function ($query) {
                        $query->where('type', 'whatsapp')
                              ->orWhereJsonContains('channels', 'whatsapp');
                    })
                    ->whereDate('created_at', $date)
                    ->count(),
            ];
        }

        // Recent messages - include multi-channel support
        $recentMessages = Message::with('recipients')
            ->latest()
            ->take(6)
            ->get()
            ->map(function ($message) {
                // Get content excerpt (200 chars max, stripped from HTML)
                $content = '';
                if ($message->isMultiChannel()) {
                    // For multi-channel, prioritize email content, then SMS, then WhatsApp
                    $content = $message->email_content ?? $message->sms_content ?? $message->whatsapp_content ?? '';
                } else {
                    // For single channel, use the appropriate content
                    $primaryChannel = $message->getPrimaryChannel();
                    $content = match($primaryChannel) {
                        'email' => $message->email_content ?? '',
                        'sms' => $message->sms_content ?? '',
                        'whatsapp' => $message->whatsapp_content ?? '',
                        default => $message->content ?? '' // Fallback to legacy content
                    };
                }

                // Strip HTML tags and limit to 200 characters
                $contentExcerpt = '';
                if ($content) {
                    $stripped = strip_tags($content);
                    $contentExcerpt = strlen($stripped) > 200 ? substr($stripped, 0, 200) . '...' : $stripped;
                }

                return [
                    'id' => $message->id,
                    'title' => $message->title,
                    'content_excerpt' => $contentExcerpt,
                    'type' => $message->getPrimaryChannel(), // Use primary channel for display
                    'channels' => $message->getEnabledChannels(), // Include all channels
                    'is_multi_channel' => $message->isMultiChannel(),
                    'status' => $message->status,
                    'recipient_count' => $message->recipients->count(),
                    'sent_count' => $message->recipients->where('status', 'sent')->count(),
                    'created_at' => $message->created_at->toISOString(),
                ];
            });

        $stats = [
            'tenants' => [
                'total' => $totalTenants,
                'active' => $activeTenants,
                'inactive' => $inactiveTenants,
                'sms_enabled' => $smsEnabledTenants,
                'email_enabled' => $emailEnabledTenants,
                'whatsapp_enabled' => $whatsappEnabledTenants,
            ],
            'messages' => [
                'total' => $totalMessages,
                'sent' => $sentMessages,
                'failed' => $failedMessages,
                'pending' => $pendingMessages,
                'drafts' => $draftMessages,
                'today' => $messagesToday,
                'this_week' => $messagesThisWeek,
                'this_month' => $messagesThisMonth,
            ],
            'recipients' => [
                'total' => $totalRecipients,
                'sent' => $sentRecipients,
                'failed' => $failedRecipients,
                'pending' => $pendingRecipients,
            ],
            'delivery_stats' => [
                'sms' => $smsDeliveryStats,
                'email' => $emailDeliveryStats,
                'whatsapp' => $whatsappDeliveryStats,
            ],
            'messages_by_type' => $messagesByType,
            'weekly_trends' => $weeklyTrends,
            'recent_messages' => $recentMessages,
        ];

        return Inertia::render('Dashboard', [
            'stats' => $stats,
        ]);
    }
}

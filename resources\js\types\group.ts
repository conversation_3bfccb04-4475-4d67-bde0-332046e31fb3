export interface Group {
    id: number;
    name: string;
    description?: string;
    color: string;
    status: boolean;
    contacts_count?: number;
    tenants_count?: number;
    created_at: string;
    updated_at: string;
}

export interface GroupFilters {
    search: string;
    status?: string[];
    per_page?: number;
}

export interface StatusCounts {
    active: number;
    inactive: number;
}

export interface SortOptions {
    field: string;
    direction: 'asc' | 'desc';
}

export interface PaginatedGroups {
    data: Group[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

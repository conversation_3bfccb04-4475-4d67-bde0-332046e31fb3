<template>
  <Head title="Groups" />
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-6 p-6">
      <!-- Header Section -->
      <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div class="space-y-4">
          <Heading
            title="Groups Management"
            description="Organize tenants into groups for targeted messaging and better management"
          />
          <div class="flex items-center gap-4 text-sm text-foreground">
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 bg-blue-500 rounded-full"></span>
              {{ props.groups.total }} {{ props.groups.total === 1 ? 'group' : 'groups' }}
            </span>
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 bg-green-500 rounded-full"></span>
              {{ props.statusCounts.active }} active
            </span>
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 bg-gray-400 rounded-full"></span>
              {{ props.statusCounts.inactive }} inactive
            </span>
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 bg-purple-500 rounded-full"></span>
              {{ totalTenants }} total tenants
            </span>
          </div>
        </div>
        <div class="flex flex-col gap-3 sm:flex-row">
          <Button size="lg" class="shadow-lg hover:shadow-xl transition-all duration-200" asChild>
            <Link href="/groups/create">
              <PlusIcon class="mr-2 h-5 w-5" />
              Create Group
            </Link>
          </Button>
        </div>
      </div>

      <FlashAlert />

      <!-- Search and Filters -->
      <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div class="flex flex-col gap-3 sm:flex-row sm:items-center">
          <!-- Search Input -->
          <div class="relative w-full max-w-lg">
            <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search groups by name or description..."
              class="pl-10 h-11 shadow-sm border-gray-200 dark:border-border-secondary bg-white dark:bg-surface-secondary min-w-[320px]"
              :model-value="search"
              @update:model-value="val => search = String(val)"
            />
          </div>

          <!-- Status Filter -->
          <DropdownMenu v-model:open="isStatusDropdownOpen">
            <DropdownMenuTrigger as-child>
              <Button variant="outline" class="h-11 shadow-sm">
                <ChevronRight class="mr-2 h-4 w-4" />
                Status Filter
                <Badge v-if="hasSelectedStatus" variant="secondary" class="ml-2 font-normal">
                  {{ selectedStatusCount }}
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" class="w-[220px]">
              <DropdownMenuLabel class="flex items-center justify-between">
                <span>Filter by status</span>
                <transition name="fade" mode="out-in">
                  <span v-if="showLoading" class="text-xs text-muted-foreground">(Loading...)</span>
                </transition>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div class="p-2">
                <div v-for="status in statuses" :key="status.value" class="relative flex items-center space-x-3 py-2 px-2 rounded-md hover:bg-muted/50">
                  <Checkbox
                    :id="status.value"
                    :modelValue="isStatusChecked(status.value)"
                    @update:modelValue="(checked) => onStatusCheckboxChange(checked, status.value)"
                    :disabled="isLoading"
                    class="peer"
                    @click.stop
                  />
                  <label
                    :for="status.value"
                    class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                    :class="{ 'opacity-50': isLoading }"
                    @click.prevent="onStatusCheckboxChange(!isStatusChecked(status.value), status.value)"
                  >
                    <span class="flex items-center gap-2">
                      <span class="h-2 w-2 rounded-full" :class="status.value === 'active' ? 'bg-green-500' : 'bg-gray-400'"></span>
                      {{ status.label }}
                    </span>
                    <Badge variant="secondary" class="text-xs">{{ status.count }}</Badge>
                  </label>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          <!-- Action Buttons -->
          <IconButton
            variant="outline"
            @click="refreshData"
            :disabled="isLoading"
            tooltip="Refresh data"
            class="h-11 w-11 shadow-sm"
          >
            <RotateCw class="h-4 w-4" :class="{ 'animate-spin': isLoading }" />
          </IconButton>
          <Button
            v-if="hasActiveFilters"
            variant="ghost"
            @click="clearFilters"
            class="h-11 text-muted-foreground hover:text-foreground"
          >
            Clear Filters
          </Button>
        </div>
      </div>

      <!-- Groups Table -->
      <DataTable
        v-model:sort="sort"
        :pagination="{
          currentPage: props.groups.current_page,
          lastPage: props.groups.last_page,
          perPage,
          total: props.groups.total,
          itemLabel: 'group'
        }"
        :is-loading="isLoading"
        loading-text="Loading groups..."
        @page-change="goToPage"
        @update:per-page="perPage = $event"
      >
        <template #default="{ sort, onSort }">
          <div class="table-container">
            <UITable class="border-0">
              <TableHeader>
                <TableRow class="table-header-row">
                  <DataTableHead :sort="sort" field="name" @sort="onSort" class="table-header-cell">
                    Group Name
                  </DataTableHead>
                  <TableHead class="table-header-cell">Description</TableHead>
                  <DataTableHead :sort="sort" field="tenants_count" @sort="onSort" class="table-header-cell">
                    Tenants
                  </DataTableHead>
                  <DataTableHead :sort="sort" field="status" @sort="onSort" class="table-header-cell">
                    Status
                  </DataTableHead>
                  <DataTableHead :sort="sort" field="created_at" @sort="onSort" class="table-header-cell">
                    Created
                  </DataTableHead>
                  <TableHead class="table-header-cell w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <!-- Empty State Row -->
                <TableRow v-if="!props.groups.data.length" class="table-row">
                  <TableCell :colspan="6" class="h-32 text-center">
                    <div class="flex flex-col items-center justify-center space-y-4 py-8">
                      <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                        <Search class="h-6 w-6 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div class="space-y-2">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                          {{ hasActiveFilters ? 'No matching groups found' : 'No groups found' }}
                        </h3>
                        <p class="text-sm text-muted-foreground max-w-sm">
                          {{ hasActiveFilters ? 'Try adjusting your search to find what you\'re looking for.' : 'Get started by adding your first group to the system.' }}
                        </p>
                      </div>
                      <div class="flex items-center gap-2">
                        <Button v-if="hasActiveFilters" variant="outline" size="sm" @click="clearFilters">
                          Clear Filters
                        </Button>
                        <Button size="sm" as-child>
                          <Link href="/groups/create">
                            <PlusIcon class="mr-1 h-3 w-3" />
                            Add Group
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
                <TableRow v-for="group in props.groups.data" :key="group.id" class="table-row">
                  <TableCell class="table-cell-primary">
                    <div class="flex items-center gap-3">
                      <div
                        class="avatar-primary text-white"
                        :style="{
                          backgroundColor: group.color + ' !important',
                          backgroundImage: 'none !important'
                        }"
                      >
                        <Users class="h-4 w-4" />
                      </div>
                      <div class="flex flex-col">
                        <Link :href="`/groups/${group.id}`" class="font-medium text-gray-900 dark:text-gray-100 hover:text-primary transition-colors cursor-pointer">
                          {{ group.name }}
                        </Link>
                        <span class="text-xs text-muted-foreground">
                          ID: #{{ group.id }}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell class="table-cell">
                    <span class="text-sm text-muted-foreground">
                      {{ group.description || '-' }}
                    </span>
                  </TableCell>
                  <TableCell class="table-cell">
                    <Link
                      v-if="(group.tenants_count || 0) > 0"
                      :href="`/tenants?groups[]=${group.id}`"
                      class="text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                    >
                      {{ group.tenants_count }}
                    </Link>
                    <span v-else class="text-sm font-medium text-muted-foreground">0</span>
                  </TableCell>
                  <TableCell class="table-cell">
                    <span
                      class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
                      :class="group.status
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'"
                    >
                      {{ group.status ? 'Active' : 'Inactive' }}
                    </span>
                  </TableCell>
                  <TableCell class="table-cell">
                    <DateTime :date="group.created_at" class="text-sm text-muted-foreground" />
                  </TableCell>
                  <TableCell class="table-cell">
                    <div class="flex items-center gap-2">
                      <ActionButton variant="outline" tooltip="View contact" as-child>
                        <Link :href="`/groups/${group.id}`">
                          <Eye class="h-4 w-4" />
                        </Link>
                      </ActionButton>
                      <ActionButton variant="outline" tooltip="Edit group" as-child>
                        <Link :href="`/groups/${group.id}/edit`">
                          <Pencil class="h-4 w-4" />
                        </Link>
                      </ActionButton>
                      <ActionButton
                        @click="confirmDelete(group)"
                        variant="destructive"
                        tooltip="Delete group"
                      >
                        <Trash2 class="h-4 w-4" />
                      </ActionButton>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </UITable>
          </div>
        </template>
      </DataTable>
    </div>

    <!-- Delete Confirmation Dialog -->
    <DeleteGroupConfirmationDialog
      v-model:open="showDeleteDialog"
      :group="groupToDelete"
      @confirmed="handleDelete"
    />
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Head, router, Link } from '@inertiajs/vue3';
import { PlusIcon, Search, ChevronRight, RotateCw, Users, Pencil, Trash2, Eye } from 'lucide-vue-next';
import AppLayout from '@/layouts/AppLayout.vue';
import DateTime from '@/components/ui/DateTime.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Table as UITable, TableHead, TableBody, TableRow, TableHeader, TableCell } from '@/components/ui/table';
import IconButton from '@/components/ui/IconButton.vue';
import ActionButton from '@/components/ui/ActionButton.vue';
import DataTable from '@/components/ui/data-table/DataTable.vue';
import DataTableHead from '@/components/ui/data-table/DataTableHead.vue';
import DeleteGroupConfirmationDialog from '@/components/Groups/DeleteGroupConfirmationDialog.vue';
import debounce from 'lodash/debounce';

import type { PaginatedGroups, Group, GroupFilters, StatusCounts, SortOptions } from '@/types/group';

interface Props {
  groups: PaginatedGroups;
  sort?: SortOptions;
  filters?: GroupFilters;
  statusCounts: StatusCounts;
}



const props = defineProps<Props>();

// State
const sort = ref<SortOptions>(props.sort || { field: 'name', direction: 'asc' });
const search = ref(props.filters?.search || '');
const selectedStatus = ref<string[]>(
  Array.isArray(props.filters?.status)
    ? props.filters.status
    : []
);
const perPage = ref<number>(props.filters?.per_page || 10);
const groupToDelete = ref<Group | null>(null);
const showDeleteDialog = ref(false);
const isLoading = ref(false);
const isStatusDropdownOpen = ref(false);

// Constants
const breadcrumbs = [{ title: 'Groups', href: '/groups' }];

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

// Computed
const hasActiveFilters = computed(() => {
  return search.value || selectedStatus.value.length > 0;
});

const totalTenants = computed(() => {
  return props.groups.data.reduce((sum, group) => sum + (group.tenants_count || 0), 0);
});

// Status-related computed properties
interface StatusOption {
  label: string;
  value: 'active' | 'inactive';
  count: number;
}

const statuses = computed<StatusOption[]>(() => [
  { label: 'Active', value: 'active', count: props.statusCounts.active },
  { label: 'Inactive', value: 'inactive', count: props.statusCounts.inactive },
]);

const hasSelectedStatus = computed(() => selectedStatus.value.length > 0);

const selectedStatusCount = computed(() => {
  return selectedStatus.value.reduce((total, status) => {
    if (status === 'active') {
      return total + (props.statusCounts?.active || 0);
    } else if (status === 'inactive') {
      return total + (props.statusCounts?.inactive || 0);
    }
    return total;
  }, 0);
});

const showLoading = computed(() => isLoading.value);



// Methods
const updateFilters = debounce(() => {
  isLoading.value = true;
  router.get('/groups', {
    search: search.value || undefined,
    status: selectedStatus.value.length > 0 ? selectedStatus.value : undefined,
    per_page: perPage.value,
    sort_field: sort.value.field,
    sort_direction: sort.value.direction,
  }, {
    preserveState: true,
    preserveScroll: true,
    onFinish: () => {
      isLoading.value = false;
    }
  });
}, 300);

const goToPage = (page: number) => {
  isLoading.value = true;
  router.get('/groups', {
    page,
    search: search.value || undefined,
    status: selectedStatus.value.length > 0 ? selectedStatus.value : undefined,
    per_page: perPage.value,
    sort_field: sort.value.field,
    sort_direction: sort.value.direction,
  }, {
    preserveState: true,
    preserveScroll: true,
    onFinish: () => {
      isLoading.value = false;
    }
  });
};

const refreshData = () => {
  isLoading.value = true;
  router.reload({
    onFinish: () => {
      isLoading.value = false;
    }
  });
};

const clearFilters = () => {
  search.value = '';
  selectedStatus.value = [];
  perPage.value = 10;
};

const isStatusChecked = (value: 'active' | 'inactive'): boolean => {
  return selectedStatus.value.includes(value);
};

const onStatusCheckboxChange = (checked: unknown, value: 'active' | 'inactive') => {
  const currentStatus = [...selectedStatus.value];
  const newStatus = checked === true
    ? [...currentStatus, value]
    : currentStatus.filter(status => status !== value);

  selectedStatus.value = newStatus;
};

const confirmDelete = (group: Group) => {
  groupToDelete.value = group;
  showDeleteDialog.value = true;
};

const handleDelete = () => {
  if (groupToDelete.value) {
    router.delete(`/groups/${groupToDelete.value.id}`, {
      onSuccess: () => {
        showDeleteDialog.value = false;
        groupToDelete.value = null;
      }
    });
  }
};

// Watchers
watch([search, selectedStatus, perPage], updateFilters);
watch(sort, updateFilters, { deep: true });
</script>

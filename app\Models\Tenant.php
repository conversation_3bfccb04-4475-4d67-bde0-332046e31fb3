<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Tenant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'secondary_email',
        'mobile_phone',
        'secondary_mobile_phone',
        'whatsapp_number',
        'secondary_whatsapp_number',
        'contact_sms',
        'contact_wa',
        'contact_email',
        'property_id',
        'unit_number',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'contact_sms' => 'boolean',
        'contact_wa' => 'boolean',
        'contact_email' => 'boolean',
        'status' => 'boolean',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'full_name',
    ];

    /**
     * Get the property that owns the tenant.
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    /**
     * Get the messages for the tenant.
     */
    public function messages()
    {
        return $this->belongsToMany(Message::class, 'message_recipients', 'tenant_id')
                    ->withPivot(['status', 'error_message', 'sent_at', 'delivered_at'])
                    ->withTimestamps();
    }

    /**
     * Get the message recipients for the tenant.
     */
    public function messageRecipients()
    {
        return $this->hasMany(MessageRecipient::class, 'tenant_id');
    }

    /**
     * Get the groups for the tenant.
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'tenant_groups', 'tenant_id');
    }

    /**
     * Get the full name of the tenant.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get the preferred contact method for messaging.
     */
    public function getPreferredContactMethod(string $type): ?string
    {
        switch ($type) {
            case 'sms':
                if (!$this->contact_sms || !$this->mobile_phone) {
                    return null;
                }
                return $this->formatPhoneNumber($this->mobile_phone);
            case 'whatsapp':
                if (!$this->contact_wa || !$this->whatsapp_number) {
                    return null;
                }
                return $this->formatPhoneNumber($this->whatsapp_number);
            case 'email':
                return $this->contact_email ? $this->email : null;
            default:
                return null;
        }
    }

    /**
     * Format phone number for messaging (Twilio-compatible format).
     *
     * Handles Canadian phone numbers and international numbers for development.
     *
     * Input formats supported:
     * - (************* → +16473492669
     * - 1 (************* → +16473492669
     * - ************ → +16473492669
     * - 6473492669 → +16473492669
     * - 16473492669 → +16473492669
     * - +6285758866491 → +6285758866491 (international, for dev)
     *
     * @param string $number The phone number to format
     * @return string Twilio-compatible phone number with + prefix
     */
    private function formatPhoneNumber(string $number): ?string
    {
        // Trim whitespace
        $number = trim($number);

        // If number already starts with +, validate and return as is (for international dev numbers)
        if (str_starts_with($number, '+')) {
            // Remove any non-digit characters except the + prefix
            $cleanNumber = '+' . preg_replace('/[^0-9]/', '', substr($number, 1));

            // Basic validation: should have at least 10 digits after +
            if (strlen($cleanNumber) >= 11) {
                return $cleanNumber;
            }

            // If invalid international format, fall through to Canadian formatting
            $number = substr($number, 1); // Remove + and process as domestic
        }

        // Remove common extensions and extra text first
        $number = preg_replace('/\s*(ext|extension|x)\s*\d+.*$/i', '', $number);

        // Remove all non-digit characters
        $number = preg_replace('/[^0-9]/', '', $number);

        // Handle empty or too short numbers
        if (strlen($number) < 10) {
           return null;
        }

        // Handle Canadian/US numbers
        if (strlen($number) == 10) {
            // 10 digits: assume Canadian number without country code
            // Example: 6473492669 → +16473492669
            return '+1' . $number;
        } elseif (strlen($number) == 11 && str_starts_with($number, '1')) {
            // 11 digits starting with 1: Canadian/US number with country code
            // Example: 16473492669 → +16473492669
            return '+' . $number;
        } elseif (strlen($number) > 11) {
            // More than 11 digits: assume international number (for dev)
            // Example: 6285758866491 → +6285758866491
            return '+' . $number;
        } else {
            // 11 digits not starting with 1: add Canadian country code
            // This handles edge cases
            return '+1' . $number;
        }
    }
}
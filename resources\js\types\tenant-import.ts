import type { Property } from './property';
import type { Tenant } from './tenant';

export interface TenantImport {
    id: number;
    session_id: string;
    first_name: string;
    last_name?: string;
    email?: string;
    secondary_email?: string;
    mobile_phone?: string;
    secondary_mobile_phone?: string;
    whatsapp_number?: string;
    secondary_whatsapp_number?: string;
    contact_sms: boolean;
    contact_wa: boolean;
    contact_email: boolean;
    property_id: number;
    property?: Property;
    unit_number?: string;
    status: boolean;
    raw_data: Record<string, any>;
    validation_errors: string[];
    has_errors: boolean;
    duplicate_action?: 'skip' | 'update' | 'create';
    duplicate_reason?: string;
    existing_tenant_id?: number;
    existing_tenant?: Tenant;
    created_at: string;
    updated_at: string;
}

export interface ImportStats {
    total: number;
    valid: number;
    errors: number;
    duplicates: number;
    new?: number;
    imported?: number;
    updated?: number;
    failed?: number;
    skipped?: number;
}

export interface ColumnMapping {
    name?: string;
    unit_number?: string;
    mobile_phone?: string;
    secondary_mobile_phone?: string;
    whatsapp_number?: string;
    secondary_whatsapp_number?: string;
    email?: string;
    secondary_email?: string;
}

export interface ImportSession {
    sessionId: string;
    propertyId: number;
    property: Property;
    filePath: string;
    totalRows: number;
    headers: string[];
    sampleData: any[][];
    columnMapping?: ColumnMapping;
    stats?: ImportStats;
}

export interface FileUploadResponse {
    success: boolean;
    headers: string[];
    sampleData: any[][];
    totalRows: number;
    property: Property;
    sessionId: string;
    suggestedMapping?: ColumnMapping;
    message?: string;
}

export interface ColumnMappingResponse {
    success: boolean;
    stats: ImportStats;
    sessionId: string;
    message?: string;
}

export interface ImportResponse {
    success: boolean;
    stats: ImportStats;
    message?: string;
}

import type { PageSize } from './pagination';

export interface ImportFilters {
    search: string;
    show_errors: 'all' | 'errors_only' | 'valid_only' | 'duplicates_only' | 'new_only';
    per_page: PageSize;
    sort_field?: string;
    sort_direction?: string;
}

export interface ContactField {
    key: keyof ColumnMapping;
    label: string;
    required: boolean;
    description?: string;
}

export const CONTACT_FIELDS: ContactField[] = [
    {
        key: 'name',
        label: 'Resident Name',
        required: false,
        description: 'Full name of the resident (will be parsed into first/last name)'
    },
    {
        key: 'unit_number',
        label: 'Unit Number',
        required: false,
        description: 'Unit or apartment number (optional)'
    },
    {
        key: 'mobile_phone',
        label: '1st Contact (Primary Phone)',
        required: false,
        description: 'Primary contact phone number'
    },
    {
        key: 'secondary_mobile_phone',
        label: '2nd Contact (Secondary Phone)',
        required: false,
        description: 'Secondary contact phone number'
    },
    {
        key: 'whatsapp_number',
        label: 'WhatsApp (1)',
        required: false,
        description: 'Primary WhatsApp number'
    },
    {
        key: 'secondary_whatsapp_number',
        label: 'WhatsApp (2)',
        required: false,
        description: 'Secondary WhatsApp number'
    },
    {
        key: 'email',
        label: 'Email Address',
        required: false,
        description: 'Primary email address'
    },
    {
        key: 'secondary_email',
        label: 'Email Address(2)',
        required: false,
        description: 'Secondary email address'
    }
];

<template>
  <AppLayout :breadcrumbs="breadcrumbs">
    <Head title="Import Contacts" />
    
    <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
      <Heading
        title="Import Contacts"
        description="Import contacts from Excel files with automatic data mapping and validation."
      />

      <FlashAlert
        v-if="uploadError"
        type="error"
        :message="uploadError"
        @close="uploadError = ''"
      />
      
      <FlashAlert :errors="form.errors" />
      
      <!-- Import Step Progress -->
      <ImportStepProgress :current-step="currentStep" :steps="steps" />
      
      <!-- Step 1: Property Selection -->
      <div v-if="currentStep === 1" class="bg-card rounded-lg border p-6">
        <h3 class="text-lg font-semibold mb-4">Step 1: Select Property</h3>
        <p class="text-muted-foreground mb-6">
          Choose the property where the imported contacts will be assigned.
        </p>
        
        <div class="max-w-md space-y-4">
          <div>
            <Label for="property" class="text-sm font-medium">Property *</Label>
            <Combobox
              id="property"
              v-model="form.property_id"
              :items="propertyOptions"
              placeholder="Select a property..."
              class="mt-2"
            />
            <InputError :message="form.errors.property_id" />
          </div>

          <div class="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Don't see your property?</span>
            <Button variant="link" size="sm" class="h-auto p-0 text-primary" as-child>
              <Link href="/properties/create">
                Create New Property
              </Link>
            </Button>
          </div>
        </div>
        
        <div class="mt-6 flex justify-end">
          <Button 
            @click="nextStep" 
            :disabled="!form.property_id"
            class="border"
          >
            Next: Upload File
            <ChevronRight class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <!-- Step 2: Enhanced File Upload -->
      <div v-if="currentStep === 2" class="bg-card rounded-lg border p-6">
        <div class="flex items-center gap-3 mb-4">
          <div class="p-2 bg-primary/10 rounded-lg">
            <Upload class="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 class="text-lg font-semibold">Step 2: Upload Excel File</h3>
            <p class="text-sm text-muted-foreground">
              Upload your contact data file for processing
            </p>
          </div>
        </div>

        <!-- File Format Info -->
        <div class="bg-muted/50 border rounded-lg p-3 mb-4">
          <div class="flex items-center gap-2 text-sm">
            <Info class="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span class="text-muted-foreground">
              Supported: <strong>.xlsx, .xls, .csv</strong> • Max: <strong>10MB</strong> • First row should contain headers
            </span>
          </div>
        </div>

        <FileUpload
          v-model="selectedFiles"
          :is-uploading="isUploading"
          :error-message="uploadError"
          description="Drag and drop your file here, or click to browse"
          @upload="handleFileUpload"
          @error="uploadError = $event"
        />

        <!-- Upload Progress -->
        <div v-if="isUploading" class="mt-6 space-y-3">
          <div class="flex items-center gap-3">
            <div class="h-4 w-4">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            </div>
            <span class="text-sm font-medium">Processing your file...</span>
          </div>
          <div class="w-full bg-muted rounded-full h-2">
            <div class="bg-primary h-2 rounded-full animate-pulse" style="width: 60%"></div>
          </div>
          <p class="text-xs text-muted-foreground">
            This may take a few moments for large files
          </p>
        </div>
        
        <div class="mt-6 flex justify-between">
          <Button variant="outline" @click="previousStep">
            <ChevronLeft class="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button 
            @click="nextStep" 
            :disabled="!uploadResponse || isUploading"
            class="border"
          >
            Next: Map Columns
            <ChevronRight class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <!-- Step 3: Column Mapping -->
      <div v-if="currentStep === 3" class="bg-card rounded-lg border p-6">
        <h3 class="text-lg font-semibold mb-4">Step 3: Map Data Columns</h3>
        <p class="text-muted-foreground mb-6">
          Map your Excel columns to contact fields. At least one of Name or Primary Phone is required.
        </p>
        
        <ColumnMapping
          v-if="uploadResponse"
          :headers="uploadResponse.headers"
          :sample-data="uploadResponse.sampleData"
          v-model="columnMapping"
          @validate="handleMappingValidation"
        />
        
        <div class="mt-6 flex justify-between">
          <Button variant="outline" @click="previousStep">
            <ChevronLeft class="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button 
            @click="processMapping" 
            :disabled="!isMappingValid || isProcessing"
            class="border"
          >
            <div v-if="isProcessing" class="mr-2 h-4 w-4">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            </div>
            Next: Review Data
            <ChevronRight class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <!-- Step 4: Confirmation -->
      <div v-if="currentStep === 4" class="bg-card rounded-lg border p-6">
        <h3 class="text-lg font-semibold mb-4">Step 4: Confirm Import</h3>
        <p class="text-muted-foreground mb-6">
          Review the import statistics and proceed to process the data.
        </p>
        
        <div v-if="importStats" class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div class="bg-muted rounded-lg p-4 text-center">
            <div class="text-2xl font-bold">{{ importStats.total }}</div>
            <div class="text-sm text-muted-foreground">Total Rows</div>
          </div>
          <div class="bg-green-50 dark:bg-green-950 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ importStats.valid }}</div>
            <div class="text-sm text-muted-foreground">Valid Records</div>
          </div>
          <div class="bg-red-50 dark:bg-red-950 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ importStats.errors }}</div>
            <div class="text-sm text-muted-foreground">Errors</div>
          </div>
          <div class="bg-yellow-50 dark:bg-yellow-950 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ importStats.duplicates }}</div>
            <div class="text-sm text-muted-foreground">Duplicates</div>
          </div>
        </div>
        
        <div class="mt-6 flex justify-between">
          <Button variant="outline" @click="previousStep">
            <ChevronLeft class="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button 
            @click="nextStep"
            class="border"
          >
            Process Data
            <ChevronRight class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <!-- Step 5: Processing -->
      <div v-if="currentStep === 5" class="bg-card rounded-lg border p-6">
        <div class="text-center py-8">
          <div class="h-8 w-8 mx-auto mb-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
          <h3 class="text-lg font-semibold mb-2">Processing Data</h3>
          <p class="text-muted-foreground">
            Please wait while we process your contact data...
          </p>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent, nextTick } from 'vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import Combobox from '@/components/ui/combobox.vue';
import FileUpload from '@/components/ui/FileUpload.vue';
import InputError from '@/components/InputError.vue';
import ImportStepProgress from '@/components/ImportStepProgress.vue';

import { cn } from '@/lib/utils';
import { Check, ChevronLeft, ChevronRight, Upload, Info } from 'lucide-vue-next';
import type { Property } from '@/types/property';
import type { BreadcrumbItem } from '@/types';
import type { FileUploadResponse, ColumnMapping as ColumnMappingType, ImportStats } from '@/types/tenant-import';

// Lazy load components for later steps
const ColumnMapping = defineAsyncComponent(() => import('./ColumnMapping.vue') as Promise<any>);

interface Props {
  properties?: Array<Property & { contactCount: number }>;
}

const props = withDefaults(defineProps<Props>(), {
  properties: () => []
});

const breadcrumbs: BreadcrumbItem[] = [
  { title: 'Import', href: '/tenant-import' },
];

const steps = [
  'Select Property',
  'Upload File', 
  'Map Columns',
  'Confirm',
  'Process',
  'Preview',
  'Complete'
];


const currentStep = ref(1);

const form = useForm({
  property_id: null as number | null,
});

const selectedFiles = ref<File[]>([]);
const isUploading = ref(false);
const uploadError = ref('');
const uploadResponse = ref<FileUploadResponse | null>(null);
const columnMapping = ref<ColumnMappingType>({});
const isMappingValid = ref(false);
const isProcessing = ref(false);
const importStats = ref<ImportStats | null>(null);

const propertyOptions = computed(() =>
  (props.properties || []).map(property => ({
    value: property.id,
    label: `${property.name} (${property.contactCount} contacts)`
  }))
);

const totalSteps = computed(() => steps.length);



const nextStep = () => {
  if (currentStep.value < totalSteps.value) {
    currentStep.value++;

    // Auto-process when reaching step 5
    if (currentStep.value === 5) {
      setTimeout(() => {
        router.get('/tenant-import/preview', {
          import_session: uploadResponse.value?.sessionId
        });
      }, 2000);
    }
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const handleFileUpload = async (files: File[]) => {
  if (!form.property_id) {
    uploadError.value = 'Please select a property first';
    return;
  }
  
  isUploading.value = true;
  uploadError.value = '';
  
  const formData = new FormData();
  formData.append('file', files[0]);
  formData.append('property_id', form.property_id.toString());
  
  try {
    const response = await fetch('/tenant-import/upload', {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        'X-Requested-With': 'XMLHttpRequest',
      },
    });
    
    const data = await response.json();
    
    if (data.success) {
      uploadResponse.value = data;
      uploadError.value = '';

      // Apply suggested mappings if available
      if (data.suggestedMapping) {
        columnMapping.value = data.suggestedMapping;
      }
    } else {
      uploadError.value = data.message || 'Upload failed';
    }
  } catch {
    uploadError.value = 'Upload failed. Please try again.';
  } finally {
    isUploading.value = false;
  }
};

const handleMappingValidation = (isValid: boolean) => {
  isMappingValid.value = isValid;
};

const processMapping = async () => {
  if (!uploadResponse.value?.sessionId) return;

  isProcessing.value = true;

  try {
    // Clean up the column mapping to remove __UNMAPPED__ values
    const cleanMapping: Record<string, string> = {};
    Object.entries(columnMapping.value).forEach(([key, value]) => {
      if (value && value !== '__UNMAPPED__') {
        cleanMapping[key] = value;
      }
    });

    console.log('Sending column mapping:', cleanMapping);
    console.log('Session ID:', uploadResponse.value.sessionId);

    const response = await fetch('/tenant-import/map-columns', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        'X-Requested-With': 'XMLHttpRequest',
      },
      body: JSON.stringify({
        import_session: uploadResponse.value.sessionId,
        column_mapping: cleanMapping,
      }),
    });

    const data = await response.json();

    if (data.success) {
      importStats.value = data.stats;
      nextStep();
    } else {
      uploadError.value = data.message || 'Processing failed';
      console.error('Processing error:', data);
      // Scroll to error message
      nextTick(() => {
        const errorElement = document.querySelector('[data-flash-alert]');
        if (errorElement) {
          errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      });
    }
  } catch (error) {
    uploadError.value = 'Processing failed. Please try again.';
    console.error('Network error:', error);
  } finally {
    isProcessing.value = false;
  }
};
</script>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('message_recipients', function (Blueprint $table) {
            // Check if contact_id column exists
            if (Schema::hasColumn('message_recipients', 'contact_id')) {
                // Drop the foreign key constraint first (if it exists)
                try {
                    $table->dropForeign(['contact_id']);
                } catch (\Exception $e) {
                    // Foreign key might not exist, continue
                }

                // Rename the column
                $table->renameColumn('contact_id', 'tenant_id');

                // Add the new foreign key constraint
                $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_recipients', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['tenant_id']);

            // Rename the column back
            $table->renameColumn('tenant_id', 'contact_id');

            // Add the original foreign key constraint
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });
    }
};

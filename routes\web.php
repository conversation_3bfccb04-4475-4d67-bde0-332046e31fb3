<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\UserController;
use App\Http\Controllers\PropertyController;
use App\Http\Controllers\TenantController;
use App\Http\Controllers\TenantImportController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\TwilioWebhookController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ArtisanController;
use App\Http\Controllers\SeederController;

Route::get('/', function () {
    return redirect()->route('login');
})->name('home');

Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Twilio webhooks (no auth required)
Route::post('/webhooks/twilio/status', [TwilioWebhookController::class, 'handleStatusWebhook'])->name('webhooks.twilio.status');

// Seeder and migration routes (no auth/session required for initial setup)
Route::prefix('seeders')->name('seeders.')->withoutMiddleware(['web'])->group(function () {
    Route::get('/', [SeederController::class, 'index'])->name('index');
    Route::get('/list', [SeederController::class, 'listSeeders'])->name('list');
    Route::post('/default-user', [SeederController::class, 'seedDefaultUser'])->name('default-user');
    Route::post('/run', [SeederController::class, 'runSeeder'])->name('run');
    Route::post('/migrate', [SeederController::class, 'runMigrations'])->name('migrate');
    Route::get('/migrate/status', [SeederController::class, 'migrationStatus'])->name('migrate.status');
    Route::post('/complete-setup', [SeederController::class, 'completeSetup'])->name('complete-setup');
});

// Test route for editors (development only)
Route::get('/test-editors', function () {
    return Inertia::render('TestEditors');
})->middleware(['auth'])->name('test.editors');

// Add routes for user management
Route::middleware(['auth'])->group(function () {
    Route::resource('users', UserController::class);

    // Property routes
    Route::get('/properties/tenant-counts', [PropertyController::class, 'getTenantCounts'])->name('properties.tenant-counts');
    Route::resource('properties', PropertyController::class);

    // Group routes
    Route::resource('groups', GroupController::class);
    Route::post('groups/{group}/add-tenants', [GroupController::class, 'addTenants'])->name('groups.add-tenants');
    Route::delete('groups/{group}/tenants/{tenant}', [GroupController::class, 'removeTenant'])->name('groups.remove-tenant');

    // Import routes
    Route::prefix('import')->name('import.')->group(function () {
        Route::get('/', [TenantImportController::class, 'index'])->name('index');
        Route::post('/upload', [TenantImportController::class, 'upload'])->name('upload');
        Route::post('/map-columns', [TenantImportController::class, 'mapColumns'])->name('map-columns');
        Route::get('/preview', [TenantImportController::class, 'preview'])->name('preview');
        Route::post('/execute', [TenantImportController::class, 'import'])->name('execute');
        Route::post('/cancel', [TenantImportController::class, 'cancel'])->name('cancel');
    });

    Route::resource('tenants', TenantController::class);
    Route::get('tenants/{tenant}/message-history', [TenantController::class, 'messageHistory'])->name('tenants.message-history');

    // Message recipient search routes
    Route::get('messages/search-recipients', [MessageController::class, 'searchRecipients'])->name('messages.search-recipients');
    Route::get('messages/all-tenants', [MessageController::class, 'getAllTenants'])->name('messages.all-tenants');

    // Message resource and control routes
    Route::resource('messages', MessageController::class);
    Route::patch('messages/{message}/pause', [MessageController::class, 'pause'])->name('messages.pause');
    Route::patch('messages/{message}/resume', [MessageController::class, 'resume'])->name('messages.resume');
    Route::patch('messages/{message}/cancel', [MessageController::class, 'cancel'])->name('messages.cancel');
    Route::patch('messages/{message}/retry', [MessageController::class, 'retry'])->name('messages.retry');
    Route::patch('messages/{message}/recipients/{recipient}/retry', [MessageController::class, 'retryRecipient'])->name('messages.recipients.retry');

    // Artisan utility routes
    Route::get('artisan', [ArtisanController::class, 'index'])->name('artisan.index');
    Route::post('artisan/execute', [ArtisanController::class, 'execute'])->name('artisan.execute');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

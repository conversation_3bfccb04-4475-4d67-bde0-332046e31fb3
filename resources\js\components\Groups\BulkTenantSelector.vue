<template>
  <div class="space-y-4">
    <!-- Search Input -->
    <div class="relative">
      <Search class="absolute left-3 top-3 h-4 w-4 text-gray-400" />
      <Input
        v-model="searchQuery"
        type="text"
        placeholder="Search tenants by name, email, or phone..."
        class="pl-10"
        @input="debouncedSearch"
      />
    </div>

    <!-- Filters -->
    <div class="flex flex-wrap gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="sm">
            <Building2 class="mr-2 h-4 w-4" />
            Property
            <Badge v-if="selectedProperties.length > 0" variant="secondary" class="ml-2">
              {{ selectedProperties.length }}
            </Badge>
            <ChevronDown class="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent class="w-56">
          <DropdownMenuLabel>Filter by Property</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div class="max-h-48 overflow-y-auto">
            <div v-for="property in properties" :key="property.id" class="flex items-center space-x-2 px-2 py-1">
              <Checkbox
                :id="`property-${property.id}`"
                :modelValue="selectedProperties.includes(property.id)"
                @update:modelValue="(checked) => toggleProperty(property.id, checked)"
              />
              <label :for="`property-${property.id}`" class="text-sm cursor-pointer flex-1">
                {{ property.name }}
              </label>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="sm">
            <Filter class="mr-2 h-4 w-4" />
            Status
            <Badge v-if="selectedStatuses.length > 0" variant="secondary" class="ml-2">
              {{ selectedStatuses.length }}
            </Badge>
            <ChevronDown class="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div class="flex items-center space-x-2 px-2 py-1">
            <Checkbox
              id="status-active"
              :modelValue="selectedStatuses.includes('active')"
              @update:modelValue="(checked) => toggleStatus('active', checked)"
            />
            <label for="status-active" class="text-sm cursor-pointer">Active</label>
          </div>
          <div class="flex items-center space-x-2 px-2 py-1">
            <Checkbox
              id="status-inactive"
              :modelValue="selectedStatuses.includes('inactive')"
              @update:modelValue="(checked) => toggleStatus('inactive', checked)"
            />
            <label for="status-inactive" class="text-sm cursor-pointer">Inactive</label>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Selection Summary -->
    <div class="flex items-center justify-between text-sm text-muted-foreground">
      <span>{{ selectedTenants.length }} of {{ filteredTenants.length }} tenants selected</span>
      <div class="flex gap-2">
        <Button
          variant="ghost"
          size="sm"
          @click="selectAll"
          :disabled="filteredTenants.length === 0"
        >
          Select All
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="clearSelection"
          :disabled="selectedTenants.length === 0"
        >
          Clear All
        </Button>
      </div>
    </div>

    <!-- Tenant List -->
    <div class="border rounded-lg">
      <div v-if="isLoading" class="p-8 text-center">
        <LoaderCircle class="h-6 w-6 animate-spin mx-auto mb-2" />
        <p class="text-sm text-muted-foreground">Loading tenants...</p>
      </div>

      <div v-else-if="filteredTenants.length === 0" class="p-8 text-center">
        <User class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p class="text-muted-foreground">No tenants found</p>
        <p class="text-sm text-muted-foreground mt-1">Try adjusting your search or filters</p>
      </div>

      <div v-else class="max-h-96 overflow-y-auto">
        <div
          v-for="tenant in filteredTenants"
          :key="tenant.id"
          class="flex items-center gap-3 p-3 border-b last:border-b-0 hover:bg-muted/50 cursor-pointer"
          @click="toggleTenant(tenant.id)"
        >
          <Checkbox
            :modelValue="selectedTenants.includes(tenant.id)"
            @update:modelValue="(checked) => toggleTenant(tenant.id, checked)"
            @click.stop
          />
          <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 flex-shrink-0">
            <User class="h-5 w-5 text-primary" />
          </div>
          <div class="flex-1 min-w-0">
            <p class="font-medium truncate">{{ tenant.full_name }}</p>
            <p class="text-sm text-muted-foreground truncate">
              {{ tenant.property?.name }}{{ tenant.unit_number ? ` - Unit ${tenant.unit_number}` : '' }}
            </p>
            <p class="text-xs text-muted-foreground truncate">
              {{ tenant.email || tenant.mobile_phone || 'No contact info' }}
            </p>
          </div>
          <div class="flex items-center gap-2">
            <span
              class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
              :class="tenant.status
                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'"
            >
              {{ tenant.status ? 'Active' : 'Inactive' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Search, User, Building2, Filter, ChevronDown, LoaderCircle } from 'lucide-vue-next';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import debounce from 'lodash/debounce';
import axios from 'axios';

interface Tenant {
  id: number;
  full_name: string;
  email?: string;
  mobile_phone?: string;
  status: boolean;
  unit_number?: string;
  property?: {
    id: number;
    name: string;
  };
}

interface Property {
  id: number;
  name: string;
}

interface Props {
  groupId: number;
  properties: Property[];
  excludeTenantIds?: number[];
}

interface Emits {
  (e: 'update:selected-tenants', value: number[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Reactive state
const searchQuery = ref('');
const selectedTenants = ref<number[]>([]);
const selectedProperties = ref<number[]>([]);
const selectedStatuses = ref<string[]>(['active']);
const tenants = ref<Tenant[]>([]);
const isLoading = ref(false);

// Computed
const filteredTenants = computed(() => {
  return tenants.value.filter(tenant => {
    // Exclude tenants already in the group
    if (props.excludeTenantIds?.includes(tenant.id)) {
      return false;
    }

    // Property filter
    if (selectedProperties.value.length > 0 && tenant.property) {
      if (!selectedProperties.value.includes(tenant.property.id)) {
        return false;
      }
    }

    // Status filter
    if (selectedStatuses.value.length > 0) {
      const tenantStatus = tenant.status ? 'active' : 'inactive';
      if (!selectedStatuses.value.includes(tenantStatus)) {
        return false;
      }
    }

    return true;
  });
});

// Methods
const loadTenants = async () => {
  isLoading.value = true;
  try {
    const response = await axios.get('/tenants', {
      params: {
        search: searchQuery.value,
        per_page: 500, // Load a reasonable amount for bulk selection
        status: ['active'], // Only load active tenants by default
      },
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    // Handle paginated response from Inertia
    if (response.data.tenants && response.data.tenants.data) {
      tenants.value = response.data.tenants.data;
    } else if (response.data.data) {
      tenants.value = response.data.data;
    } else {
      tenants.value = [];
    }
  } catch (error) {
    console.error('Error loading tenants:', error);
    tenants.value = [];
  } finally {
    isLoading.value = false;
  }
};

const debouncedSearch = debounce(() => {
  loadTenants();
}, 300);

const toggleTenant = (tenantId: number, checked?: boolean) => {
  const isSelected = selectedTenants.value.includes(tenantId);
  const shouldSelect = checked !== undefined ? checked : !isSelected;

  if (shouldSelect && !isSelected) {
    selectedTenants.value.push(tenantId);
  } else if (!shouldSelect && isSelected) {
    selectedTenants.value = selectedTenants.value.filter(id => id !== tenantId);
  }

  emit('update:selected-tenants', selectedTenants.value);
};

const toggleProperty = (propertyId: number, checked: boolean) => {
  if (checked) {
    selectedProperties.value.push(propertyId);
  } else {
    selectedProperties.value = selectedProperties.value.filter(id => id !== propertyId);
  }
};

const toggleStatus = (status: string, checked: boolean) => {
  if (checked) {
    selectedStatuses.value.push(status);
  } else {
    selectedStatuses.value = selectedStatuses.value.filter(s => s !== status);
  }
};

const selectAll = () => {
  selectedTenants.value = filteredTenants.value.map(tenant => tenant.id);
  emit('update:selected-tenants', selectedTenants.value);
};

const clearSelection = () => {
  selectedTenants.value = [];
  emit('update:selected-tenants', selectedTenants.value);
};

// Watchers
watch([selectedProperties, selectedStatuses], () => {
  // Clear selection when filters change to avoid confusion
  clearSelection();
}, { deep: true });

// Initialize
onMounted(() => {
  loadTenants();
});
</script>

<template>
  <Head :title="group.name" />
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-6 p-6">
      <!-- Header Section -->
      <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div class="space-y-4">
          <Heading
            :title="group.name"
            :description="`View and manage group information for ${group.name}`"
          />
          <div class="flex items-center gap-4 text-sm text-foreground">
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 rounded-full" :class="group.status ? 'bg-green-500' : 'bg-gray-400'"></span>
              {{ group.status ? 'Active Group' : 'Inactive Group' }}
            </span>
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 bg-blue-500 rounded-full"></span>
              ID: #{{ group.id }}
            </span>
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 bg-purple-500 rounded-full"></span>
              {{ group.tenants?.length || 0 }} {{ (group.tenants?.length || 0) === 1 ? 'tenant' : 'tenants' }}
            </span>
          </div>
        </div>
        <div class="flex flex-col gap-3 sm:flex-row">
          <Button variant="outline" size="lg" class="shadow-sm hover:shadow-md transition-all duration-200" asChild>
            <Link :href="`/groups/${group.id}/edit`">
              <Pencil class="mr-2 h-5 w-5" />
              Edit Group
            </Link>
          </Button>
          <Button size="lg" class="shadow-lg hover:shadow-xl transition-all duration-200" asChild>
            <Link :href="`/messages/create?group=${group.id}`">
              <MessageSquare class="mr-2 h-5 w-5" />
              Send Message
            </Link>
          </Button>
        </div>
      </div>

      <FlashAlert />

      <!-- Content Grid -->
      <div class="grid gap-6 lg:grid-cols-3">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Group Information -->
          <Card class="view-card">
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Users class="h-5 w-5" />
                Group Information
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-6">
              <div class="grid gap-6 sm:grid-cols-2">
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Group Name</Label>
                  <p class="mt-1 text-sm">{{ group.name }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Status</Label>
                  <p class="mt-1">
                    <span
                      class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
                      :class="group.status
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'"
                    >
                      {{ group.status ? 'Active' : 'Inactive' }}
                    </span>
                  </p>
                </div>
              </div>
              
              <div v-if="group.description">
                <Label class="text-sm font-medium text-muted-foreground">Description</Label>
                <p class="mt-1 text-sm">{{ group.description }}</p>
              </div>

              <div class="grid gap-6 sm:grid-cols-2">
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Created</Label>
                  <p class="mt-1 text-sm">
                    <DateTime :date="group.created_at" />
                  </p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Last Updated</Label>
                  <p class="mt-1 text-sm">
                    <DateTime :date="group.updated_at" />
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Tenants in Group -->
          <Card class="view-card">
            <CardHeader>
              <div class="flex items-center justify-between">
                <CardTitle class="flex items-center gap-2">
                  <User class="h-5 w-5" />
                  Tenants in Group ({{ groupTenants?.total || 0 }})
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  @click="showBulkAddDialog = true"
                  class="shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <UserPlus class="mr-2 h-4 w-4" />
                  Add Tenants
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <!-- Search and Filters -->
              <div class="flex flex-col gap-4 mb-4">
                <div class="flex flex-col gap-3 sm:flex-row sm:items-center">
                  <!-- Search Input -->
                  <div class="relative w-full max-w-md">
                    <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search tenants..."
                      class="pl-10 h-9 text-sm"
                      :model-value="search"
                      @update:model-value="val => search = String(val)"
                    />
                  </div>

                  <!-- Status Filter -->
                  <DropdownMenu v-model:open="isStatusDropdownOpen">
                    <DropdownMenuTrigger as-child>
                      <Button variant="outline" size="sm" class="h-9">
                        <ChevronRight class="mr-2 h-3 w-3" />
                        Status
                        <Badge v-if="selectedStatus.length > 0" variant="secondary" class="ml-2 text-xs">
                          {{ selectedStatus.length }}
                        </Badge>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" class="w-[180px]">
                      <DropdownMenuLabel>Filter by status</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <div class="p-2">
                        <div v-for="status in statuses" :key="status.value" class="relative flex items-center space-x-2 py-1">
                          <Checkbox
                            :id="`group-status-${status.value}`"
                            :modelValue="isStatusChecked(status.value)"
                            @update:modelValue="(checked) => onStatusCheckboxChange(checked, status.value)"
                            :disabled="isLoading"
                            class="peer"
                            @click.stop
                          />
                          <label
                            :for="`group-status-${status.value}`"
                            class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                            :class="{ 'opacity-50': isLoading }"
                            @click.prevent="onStatusCheckboxChange(!isStatusChecked(status.value), status.value)"
                          >
                            <span class="flex items-center gap-2">
                              <span class="h-2 w-2 rounded-full" :class="status.value === 'active' ? 'bg-green-500' : 'bg-gray-400'"></span>
                              {{ status.label }}
                            </span>
                          </label>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  <!-- Action Buttons -->
                  <IconButton
                    variant="outline"
                    @click="updateFilters()"
                    :disabled="isLoading"
                    tooltip="Refresh data"
                    class="h-9 w-9"
                  >
                    <RotateCw class="h-3 w-3" :class="{ 'animate-spin': isLoading }" />
                  </IconButton>
                  <Button
                    v-if="hasActiveFilters"
                    variant="ghost"
                    @click="resetFilters"
                    size="sm"
                    class="h-9 text-muted-foreground hover:text-foreground"
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>

              <!-- Tenants Table -->
              <DataTable
                v-model:sort="sort"
                :pagination="{
                  currentPage: groupTenants?.current_page || 1,
                  lastPage: groupTenants?.last_page || 1,
                  perPage,
                  total: groupTenants?.total || 0,
                  itemLabel: 'tenant'
                }"
                :is-loading="isLoading"
                loading-text="Loading tenants..."
                @page-change="goToPage"
                @update:per-page="perPage = $event"
              >
                <template #default="{ sort, onSort }">
                  <div class="table-container">
                    <UITable class="border-0">
                      <TableHeader>
                        <TableRow class="table-header-row">
                          <DataTableHead :sort="sort" field="first_name" @sort="onSort" class="table-header-cell">
                            Tenant
                          </DataTableHead>
                          <DataTableHead :sort="sort" field="email" @sort="onSort" class="table-header-cell">
                            Email & Phone
                          </DataTableHead>
                          <DataTableHead :sort="sort" field="property_id" @sort="onSort" class="table-header-cell">
                            Property & Unit
                          </DataTableHead>
                          <DataTableHead :sort="sort" field="status" @sort="onSort" class="table-header-cell">
                            Status
                          </DataTableHead>
                          <TableHead class="table-header-cell">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <!-- Empty State Row -->
                        <TableRow v-if="!groupTenants?.data?.length" class="table-row">
                          <TableCell :colspan="6" class="h-32 text-center">
                            <div class="flex flex-col items-center justify-center space-y-4 py-8">
                              <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                                <User class="h-6 w-6 text-gray-400 dark:text-gray-500" />
                              </div>
                              <div class="space-y-2">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                  {{ hasActiveFilters ? 'No matching tenants found' : 'No tenants in this group yet' }}
                                </h3>
                                <p class="text-sm text-muted-foreground max-w-sm">
                                  {{ hasActiveFilters ? 'Try adjusting your search or filters to find what you\'re looking for.' : 'Add tenants to this group to get started.' }}
                                </p>
                              </div>
                              <div class="flex items-center gap-2">
                                <Button v-if="hasActiveFilters" variant="outline" size="sm" @click="resetFilters">
                                  Clear Filters
                                </Button>
                                <Button size="sm" @click="showBulkAddDialog = true">
                                  <UserPlus class="mr-1 h-3 w-3" />
                                  Add Tenants
                                </Button>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>

                        <!-- Data Rows -->
                        <TableRow
                          v-for="tenant in groupTenants?.data || []"
                          :key="tenant.id"
                          class="table-row"
                        >
                          <!-- Tenant Info -->
                          <TableCell class="table-cell-primary">
                            <div class="flex items-center gap-2">
                              <div class="avatar-primary">
                                <User class="h-3 w-3" />
                              </div>
                              <div class="flex flex-col">
                                <Link
                                  :href="`/tenants/${tenant.id}`"
                                  class="font-medium text-gray-900 dark:text-gray-100 hover:text-primary transition-colors cursor-pointer text-sm"
                                >
                                  {{ tenant.first_name }} {{ tenant.last_name }}
                                </Link>
                                <span class="text-xs text-muted-foreground">
                                  ID: #{{ tenant.id }}
                                </span>
                              </div>
                            </div>
                          </TableCell>

                          <!-- Email & Phone -->
                          <TableCell class="table-cell">
                            <div class="flex flex-col gap-1">
                              <div v-if="tenant.email" class="flex items-center gap-1">
                                <Mail class="h-3 w-3 text-muted-foreground" />
                                <span class="text-xs">{{ tenant.email }}</span>
                              </div>
                              <div v-if="tenant.mobile_phone" class="flex items-center gap-1">
                                <Phone class="h-3 w-3 text-muted-foreground" />
                                <span class="text-xs">{{ tenant.mobile_phone }}</span>
                              </div>
                              <div v-if="!tenant.email && !tenant.mobile_phone" class="text-xs text-muted-foreground">
                                No contact info
                              </div>
                            </div>
                          </TableCell>

                          <!-- Property & Unit -->
                          <TableCell class="table-cell">
                            <div class="flex flex-col gap-1">
                              <div v-if="tenant.property" class="flex items-center gap-1">
                                <Building class="h-3 w-3 text-muted-foreground" />
                                <span class="text-xs font-medium">{{ tenant.property.name }}</span>
                              </div>
                              <div v-if="tenant.unit_number" class="flex items-center gap-1 ml-4">
                                <span class="text-xs text-muted-foreground">Unit: {{ tenant.unit_number }}</span>
                              </div>
                              <div v-if="!tenant.property" class="text-xs text-muted-foreground">
                                No property assigned
                              </div>
                            </div>
                          </TableCell>

                          <!-- Status -->
                          <TableCell class="table-cell">
                            <div class="flex items-center gap-2">
                              <div v-if="tenant.status" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                <div class="h-1 w-1 bg-green-500 rounded-full"></div>
                                Active
                              </div>
                              <div v-else class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                                <div class="h-1 w-1 bg-gray-500 rounded-full"></div>
                                Inactive
                              </div>
                            </div>
                          </TableCell>

                          <!-- Actions -->
                          <TableCell class="table-cell">
                            <div class="flex items-center gap-1">
                              <ActionButton variant="outline" tooltip="View tenant" as-child size="sm">
                                <Link :href="`/tenants/${tenant.id}`">
                                  <Eye class="h-3 w-3" />
                                </Link>
                              </ActionButton>
                              <ActionButton variant="outline" tooltip="Edit tenant" as-child size="sm">
                                <Link :href="`/tenants/${tenant.id}/edit`">
                                  <Pencil class="h-3 w-3" />
                                </Link>
                              </ActionButton>
                              <ActionButton
                                @click="confirmRemoveFromGroup(tenant)"
                                variant="destructive"
                                tooltip="Remove from group"
                                size="sm"
                              >
                                <UserMinus class="h-3 w-3" />
                              </ActionButton>
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </UITable>
                  </div>
                </template>
              </DataTable>
            </CardContent>
          </Card>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Details Card -->
          <Card class="sidebar-card-details">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Info class="h-4 w-4" />
                Details
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content">
              <div class="space-y-3">
                <!-- Group ID -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Hash class="h-4 w-4" />
                    Group ID
                  </span>
                  <span class="text-sm font-medium">#{{ group.id }}</span>
                </div>

                <!-- Created -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Created
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="group.created_at" type="absolute" /></span>
                </div>

                <!-- Last Updated -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Last Updated
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="group.updated_at" type="absolute" /></span>
                </div>

                <!-- Status -->
                <div class="flex items-center justify-between py-2">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <ToggleLeft class="h-4 w-4" />
                    Status
                  </span>
                  <div v-if="group.status" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                    Active
                  </div>
                  <div v-else class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                    <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                    Inactive
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Quick Actions -->
          <Card class="sidebar-card-help">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Zap class="h-4 w-4" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content-help">
              <div class="space-y-3">
                <!-- Send Message -->
                <Button
                  variant="outline"
                  size="sm"
                  class="w-full justify-start"
                  asChild
                >
                  <Link :href="`/messages/create?group_id=${group.id}`">
                    <MessageSquare class="mr-2 h-4 w-4" />
                    Send Message to Group
                  </Link>
                </Button>

                <!-- Divider -->
                <div class="border-t border-gray-200 dark:border-gray-700 my-3"></div>

                <!-- Edit Group -->
                <Button
                  variant="outline"
                  size="sm"
                  class="w-full justify-start"
                  asChild
                >
                  <Link :href="`/groups/${group.id}/edit`">
                    <Pencil class="mr-2 h-4 w-4" />
                    Edit Group
                  </Link>
                </Button>

                <!-- View All Groups -->
                <Button
                  variant="outline"
                  size="sm"
                  class="w-full justify-start"
                  asChild
                >
                  <Link href="/groups">
                    <Users class="mr-2 h-4 w-4" />
                    View All Groups
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>

    <!-- Bulk Add Tenants Dialog -->
    <Dialog v-model:open="showBulkAddDialog">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Add Tenants to {{ group.name }}</DialogTitle>
          <DialogDescription>
            Select tenants to add to this group. Tenants already in the group will be excluded.
          </DialogDescription>
        </DialogHeader>

        <div class="flex-1 overflow-hidden">
          <BulkTenantSelector
            :group-id="group.id"
            :properties="properties"
            :exclude-tenant-ids="existingTenantIds"
            @update:selected-tenants="selectedTenantIds = $event"
          />
        </div>

        <DialogFooter class="flex-shrink-0">
          <Button variant="outline" @click="showBulkAddDialog = false">
            Cancel
          </Button>
          <Button
            @click="addSelectedTenants"
            :disabled="selectedTenantIds.length === 0 || isAddingTenants"
          >
            <LoaderCircle v-if="isAddingTenants" class="mr-2 h-4 w-4 animate-spin" />
            Add {{ selectedTenantIds.length }} Tenant{{ selectedTenantIds.length === 1 ? '' : 's' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Remove Tenant Confirmation Dialog -->
    <AlertDialog v-model:open="showRemoveDialog">
      <AlertDialogContent>
        <AlertDialogTitle>Remove Tenant from Group</AlertDialogTitle>
        <AlertDialogDescription>
          Are you sure you want to remove {{ tenantToRemove?.first_name }} {{ tenantToRemove?.last_name }} from this group? This action cannot be undone.
        </AlertDialogDescription>
        <AlertDialogFooter>
          <AlertDialogCancel @click="showRemoveDialog = false">Cancel</AlertDialogCancel>
          <AlertDialogAction @click="removeFromGroup">Remove</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import BulkTenantSelector from '@/components/Groups/BulkTenantSelector.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Table as UITable, TableHead, TableBody, TableRow, TableHeader, TableCell } from '@/components/ui/table';
import { AlertDialog, AlertDialogContent, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from '@/components/ui/alert-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import ActionButton from '@/components/ui/ActionButton.vue';
import IconButton from '@/components/ui/IconButton.vue';
import DataTable from '@/components/ui/data-table/DataTable.vue';
import DataTableHead from '@/components/ui/data-table/DataTableHead.vue';
import {
  Pencil,
  MessageSquare,
  User,
  Users,
  UserPlus,
  UserMinus,
  Zap,
  Info,
  Hash,
  Calendar,
  ToggleLeft,
  LoaderCircle,
  Search,
  ChevronRight,
  RotateCw,
  Mail,
  Phone,
  Building,
  Eye,
  MessageCircle
} from 'lucide-vue-next';
import DateTime from '@/components/ui/DateTime.vue';
import { PageSize } from '@/types/pagination';
import type { Group } from '@/types/group';
import type { Tenant, PaginatedTenants, SortOptions } from '@/types/tenant';
import type { Property } from '@/types/property';
import type { BreadcrumbItem } from '@/types';
import axios from 'axios';
import debounce from 'lodash/debounce';

interface Props {
  group: Group & {
    tenants?: Tenant[];
  };
  properties: Property[];
  groupTenants?: PaginatedTenants;
  sort?: SortOptions;
  filters?: {
    search?: string;
    status?: string[];
    per_page?: PageSize;
  };
}

const props = defineProps<Props>();

// Reactive state
const showBulkAddDialog = ref(false);
const selectedTenantIds = ref<number[]>([]);
const isAddingTenants = ref(false);
const showRemoveDialog = ref(false);
const tenantToRemove = ref<Tenant | null>(null);

// Table state
const sort = ref<SortOptions>(props.sort || { field: 'first_name', direction: 'asc' });
const search = ref(props.filters?.search || '');
const selectedStatus = ref<('active' | 'inactive')[]>(
  Array.isArray(props.filters?.status)
    ? props.filters.status.filter((status): status is 'active' | 'inactive' => status === 'active' || status === 'inactive')
    : []
);
const perPage = ref<PageSize>(props.filters?.per_page || PageSize.Small);
const isLoading = ref(false);
const isStatusDropdownOpen = ref(false);

// Computed
const existingTenantIds = computed(() => {
  return props.group.tenants?.map(tenant => tenant.id) || [];
});

const statuses = computed(() => [
  { label: 'Active', value: 'active' as const },
  { label: 'Inactive', value: 'inactive' as const },
]);

const hasActiveFilters = computed(() => {
  return Boolean(search.value) || selectedStatus.value.length > 0;
});

const breadcrumbs: BreadcrumbItem[] = [
  { title: 'Groups', href: '/groups' },
  { title: props.group.name, href: `/groups/${props.group.id}` },
];

// Methods
const isStatusChecked = (value: 'active' | 'inactive'): boolean => {
  return selectedStatus.value.includes(value);
};

const onStatusCheckboxChange = (checked: unknown, value: 'active' | 'inactive') => {
  const currentStatus = [...selectedStatus.value];
  const newStatus = checked === true
    ? [...currentStatus, value]
    : currentStatus.filter(status => status !== value);

  selectedStatus.value = newStatus;
  debouncedUpdateFilters();
};

const updateFilters = (params = {}) => {
  isLoading.value = true;
  router.visit(
    route('groups.show', {
      group: props.group.id,
      search: search.value,
      status: selectedStatus.value,
      sort: sort.value,
      per_page: perPage.value,
      ...params
    }),
    {
      preserveState: true,
      preserveScroll: true,
      replace: true,
      only: ['groupTenants', 'filters'],
      onFinish: () => {
        isLoading.value = false;
      },
      onError: () => {
        isLoading.value = false;
      }
    }
  );
};

const debouncedUpdateFilters = debounce((params = {}) => {
  updateFilters(params);
}, 300);

const goToPage = (page: number) => {
  updateFilters({ page });
};

const resetFilters = () => {
  search.value = '';
  selectedStatus.value = [];
  updateFilters();
};

const confirmRemoveFromGroup = (tenant: Tenant) => {
  tenantToRemove.value = tenant;
  showRemoveDialog.value = true;
};

const removeFromGroup = () => {
  if (!tenantToRemove.value) return;

  router.delete(`/groups/${props.group.id}/tenants/${tenantToRemove.value.id}`, {
    preserveScroll: true,
    preserveState: true,
    onSuccess: () => {
      showRemoveDialog.value = false;
      tenantToRemove.value = null;
    },
    onError: (error) => {
      console.error('Error removing tenant from group:', error);
    }
  });
};

const addSelectedContacts = async () => {
  if (selectedContactIds.value.length === 0) return;

  isAddingContacts.value = true;
  try {
    const response = await axios.post(`/groups/${props.group.id}/add-contacts`, {
      contact_ids: selectedContactIds.value
    });

    if (response.data.success) {
      // Close dialog and reset state
      showBulkAddDialog.value = false;
      selectedContactIds.value = [];

      // Refresh the page to show updated contact list with success message
      router.reload({
        only: ['group', 'groupContacts'],
        onSuccess: () => {
          // The success message will be handled by the backend response
        }
      });
    }
  } catch (error: any) {
    console.error('Error adding contacts to group:', error);

    // Handle validation or server errors
    if (error.response?.data?.message) {
      // You could add a toast notification here or handle the error message
      alert(error.response.data.message);
    } else {
      alert('An error occurred while adding contacts to the group. Please try again.');
    }
  } finally {
    isAddingContacts.value = false;
  }
};

// Watchers
watch(search, () => debouncedUpdateFilters());
watch(sort, () => updateFilters(), { deep: true });
watch(perPage, () => {
  updateFilters({ page: 1 }); // Reset to page 1 when changing per page
});
</script>

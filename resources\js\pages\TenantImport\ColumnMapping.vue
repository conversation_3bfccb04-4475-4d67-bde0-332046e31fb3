<template>
  <div class="space-y-6 w-full">
    <!-- Enhanced Sample Data Preview -->
    <div class="bg-gradient-to-br from-muted/50 to-muted rounded-lg border p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-primary/10 rounded-lg">
            <Database class="h-5 w-5 text-primary" />
          </div>
          <div>
            <h4 class="font-medium">Sample Data Preview</h4>
            <p class="text-xs text-muted-foreground">First 3 rows from your file</p>
          </div>
        </div>
        <div class="hidden sm:flex items-center gap-2 text-xs text-muted-foreground">
          <ArrowLeftRight class="h-3 w-3" />
          <span>Scroll horizontally to see all columns</span>
        </div>
      </div>
      <div class="w-full">
      <ScrollArea class="border rounded-md bg-background h-[300px]" orientation="both">
        <table class="text-sm border-collapse min-w-[600px] block w-full max-w-2xl">
          <thead>
            <tr class="border-b bg-muted/50">
              <th
                v-for="(header, index) in (headers || [])"
                :key="index"
                class="text-left p-2 font-medium border-r last:border-r-0 min-w-[120px]"
                :title="header"
              >
                {{ header }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(row, rowIndex) in (sampleData || []).slice(0, 3)"
              :key="rowIndex"
              class="border-b last:border-b-0 hover:bg-muted/30"
            >
              <td
                v-for="(cell, cellIndex) in row"
                :key="cellIndex"
                class="p-2 text-muted-foreground border-r last:border-r-0 min-w-[120px]"
                :title="cell || '-'"
              >
                {{ cell || '-' }}
              </td>
            </tr>
          </tbody>
        </table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
      </div>
      <p class="text-xs text-muted-foreground mt-2">
        Showing first {{ Math.min(3, (sampleData || []).length) }} rows of sample data
      </p>
    </div>
    
    <!-- Enhanced Column Mapping -->
    <div class="space-y-6">
      <div class="flex items-center gap-3">
        <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
          <ArrowLeftRight class="h-5 w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h4 class="font-medium">Map Excel Columns to Contact Fields</h4>
          <p class="text-sm text-muted-foreground">
            Connect your file columns to the appropriate contact fields. Required fields are marked with a red badge.
          </p>
        </div>
      </div>

      <!-- Mapping Requirements -->
      <div class="bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
        <div class="flex items-start gap-3">
          <AlertTriangle class="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
          <div>
            <h5 class="font-medium text-amber-800 dark:text-amber-200 mb-1">Mapping Requirements</h5>
            <p class="text-sm text-amber-700 dark:text-amber-300">
              At least one of <strong>Name</strong> or <strong>Primary Phone</strong> must be mapped to proceed with the import.
            </p>
          </div>
        </div>
      </div>
      
      <div class="grid gap-4">
        <div
          v-for="field in CONTACT_FIELDS"
          :key="field.key"
          :class="cn(
            'grid grid-cols-1 lg:grid-cols-3 gap-4 items-start p-5 border rounded-lg transition-all duration-200',
            modelValue[field.key] && modelValue[field.key] !== '__UNMAPPED__'
              ? 'border-muted-foreground/30 bg-muted/30'
              : 'border-border hover:border-muted-foreground/50 hover:shadow-sm'
          )"
        >
          <div class="space-y-2">
            <Label :for="field.key" class="flex items-center gap-2 font-medium">
              <div class="flex items-center gap-2">
                <div :class="cn(
                  'w-2 h-2 rounded-full',
                  modelValue[field.key] && modelValue[field.key] !== '__UNMAPPED__'
                    ? 'bg-foreground'
                    : field.required
                      ? 'bg-red-500'
                      : 'bg-muted-foreground/50'
                )" />
                {{ field.label }}
              </div>
              <div class="flex gap-1">
                <Badge v-if="field.required" variant="destructive" class="text-xs px-2 py-0.5">Required</Badge>
                <Badge v-if="modelValue[field.key] && modelValue[field.key] !== '__UNMAPPED__' && !manuallyChanged.has(field.key)"
                       variant="secondary" class="text-xs px-2 py-0.5 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                  Auto-mapped
                </Badge>
                <Badge v-if="modelValue[field.key] && modelValue[field.key] !== '__UNMAPPED__'"
                       variant="outline" class="text-xs px-2 py-0.5">
                  Mapped
                </Badge>
              </div>
            </Label>
            <p v-if="field.description" class="text-xs text-muted-foreground leading-relaxed">
              {{ field.description }}
            </p>
          </div>
          
          <div class="lg:col-span-2">
            <Select
              :model-value="modelValue[field.key] || '__UNMAPPED__'"
              @update:model-value="updateMapping(field.key, $event as string)"
            >
              <SelectTrigger>
                <SelectValue :placeholder="`Select column for ${field.label.toLowerCase()}`" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__UNMAPPED__">
                  <span class="text-muted-foreground">-- Not mapped --</span>
                </SelectItem>
                <SelectItem
                  v-for="(header, index) in (headers || [])"
                  :key="index"
                  :value="header"
                >
                  {{ header }}
                </SelectItem>
              </SelectContent>
            </Select>
            
            <!-- Enhanced sample data display -->
            <div v-if="modelValue[field.key] && modelValue[field.key] !== '__UNMAPPED__'"
                 class="mt-3 p-3 bg-muted/30 border rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <Database class="h-3 w-3 text-muted-foreground" />
                <span class="font-medium text-xs">Sample values from "{{ modelValue[field.key] }}":</span>
              </div>
              <div class="space-y-1">
                <div
                  v-for="(row, index) in (sampleData || []).slice(0, 3)"
                  :key="index"
                  class="text-xs px-2 py-1 bg-background rounded border"
                >
                  {{ getColumnValue(row, modelValue[field.key] || '') || '(empty)' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Enhanced Validation Messages -->
    <div v-if="validationMessages.length > 0" class="space-y-3">
      <h5 class="font-medium text-sm">Validation Status</h5>
      <div class="space-y-2">
        <div
          v-for="(message, index) in validationMessages"
          :key="index"
          :class="cn(
            'p-4 rounded-lg text-sm border transition-all duration-200',
            message.type === 'error' ? 'bg-red-50 dark:bg-red-950 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800 shadow-sm' :
            message.type === 'warning' ? 'bg-yellow-50 dark:bg-yellow-950 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800 shadow-sm' :
            'bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800 shadow-sm'
          )"
        >
          <div class="flex items-start gap-3">
            <div :class="cn(
              'p-1 rounded-full',
              message.type === 'error' ? 'bg-red-100 dark:bg-red-900' :
              message.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900' :
              'bg-blue-100 dark:bg-blue-900'
            )">
              <AlertCircle v-if="message.type === 'error'" class="h-4 w-4 flex-shrink-0" />
              <AlertTriangle v-else-if="message.type === 'warning'" class="h-4 w-4 flex-shrink-0" />
              <Info v-else class="h-4 w-4 flex-shrink-0" />
            </div>
            <span class="leading-relaxed">{{ message.text }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Enhanced Mapping Summary -->
    <div v-if="mappedFields.length > 0" class="bg-muted/30 rounded-lg border p-6">
      <div class="flex items-center gap-3 mb-4">
        <div class="p-2 bg-muted rounded-lg">
          <Check class="h-5 w-5 text-muted-foreground" />
        </div>
        <div>
          <h5 class="font-medium">Successfully Mapped Fields</h5>
          <p class="text-sm text-muted-foreground">{{ mappedFields.length }} field{{ mappedFields.length !== 1 ? 's' : '' }} ready for import</p>
        </div>
      </div>
      <div class="grid gap-3 sm:grid-cols-2">
        <div
          v-for="field in mappedFields"
          :key="field.key"
          class="flex items-center justify-between p-3 bg-background rounded-lg border"
        >
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-foreground rounded-full" />
            <span class="font-medium text-sm">{{ field.label }}</span>
          </div>
          <span class="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
            {{ modelValue[field.key] }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { AlertCircle, AlertTriangle, Info, Database, ArrowLeftRight, Check } from 'lucide-vue-next';
import { CONTACT_FIELDS, type ColumnMapping } from '@/types/contact-import';

interface Props {
  headers: string[];
  sampleData: any[][];
  modelValue: ColumnMapping;
}

interface Emits {
  (e: 'update:modelValue', value: ColumnMapping): void;
  (e: 'validate', isValid: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  headers: () => [],
  sampleData: () => [],
  modelValue: () => ({} as ColumnMapping)
});
const emit = defineEmits<Emits>();

// Track which fields have been manually changed
const manuallyChanged = ref<Set<string>>(new Set());

interface ValidationMessage {
  type: 'error' | 'warning' | 'info';
  text: string;
}

const mappedFields = computed(() =>
  CONTACT_FIELDS.filter(field => props.modelValue[field.key] && props.modelValue[field.key] !== '__UNMAPPED__')
);

const validationMessages = computed((): ValidationMessage[] => {
  const messages: ValidationMessage[] = [];
  
  // Check if at least name or phone is mapped
  const hasName = !!props.modelValue.name;
  const hasPhone = !!props.modelValue.mobile_phone;
  
  if (!hasName && !hasPhone) {
    messages.push({
      type: 'error',
      text: 'Either Name or Primary Phone must be mapped to proceed.'
    });
  }
  
  // Unit number is optional - no validation needed
  
  // Check for duplicate mappings
  const mappedColumns = Object.values(props.modelValue).filter(Boolean);
  const uniqueColumns = new Set(mappedColumns);
  if (mappedColumns.length !== uniqueColumns.size) {
    messages.push({
      type: 'warning',
      text: 'Some columns are mapped to multiple fields. This may cause data conflicts.'
    });
  }
  
  // Provide helpful info
  if (mappedFields.value.length > 0) {
    messages.push({
      type: 'info',
      text: `${mappedFields.value.length} field(s) mapped successfully.`
    });
  }
  
  return messages;
});

const isValid = computed(() => {
  return !validationMessages.value.some(msg => msg.type === 'error');
});

const updateMapping = (fieldKey: keyof ColumnMapping, columnName: string) => {
  const newMapping = { ...props.modelValue };

  // Mark this field as manually changed
  manuallyChanged.value.add(fieldKey);

  if (columnName === '__UNMAPPED__' || columnName === '') {
    delete newMapping[fieldKey];
  } else {
    newMapping[fieldKey] = columnName;
  }

  emit('update:modelValue', newMapping);
};

const getColumnValue = (row: any[], columnName: string): string => {
  if (!row || !columnName || !props.headers) return '';
  const columnIndex = props.headers.indexOf(columnName);
  return columnIndex >= 0 ? (row[columnIndex] || '') : '';
};

// Watch for validation changes
watch(isValid, (newValue) => {
  emit('validate', newValue);
}, { immediate: true });
</script>

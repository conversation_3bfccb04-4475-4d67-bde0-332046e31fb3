<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\Group;
use App\Models\Property;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TenantController extends Controller
{    public function index(Request $request)
    {
        $sort = $request->input('sort', ['field' => 'id', 'direction' => 'asc']);
        $search = $request->input('search');
        $statusFilter = $request->input('status', []);
        $buildingFilter = $request->input('buildings', []);
        $groupFilter = $request->input('groups', []);
        $perPage = $request->input('per_page', 10);

        $query = Tenant::query()->with('property');

        // Apply search filter
        if ($search) {
            $search = strtolower(trim($search));
            $query->where(function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('secondary_email', 'like', "%{$search}%")
                  ->orWhere('mobile_phone', 'like', "%{$search}%")
                  ->orWhere('secondary_mobile_phone', 'like', "%{$search}%")
                  ->orWhere('whatsapp_number', 'like', "%{$search}%")
                  ->orWhere('secondary_whatsapp_number', 'like', "%{$search}%")
                  ->orWhere('unit_number', 'like', "%{$search}%");
            });
        }

        // Get filtered status counts
        $filteredQuery = clone $query;
        $statusCounts = [
            'active' => $filteredQuery->clone()->where('status', true)->count(),
            'inactive' => $filteredQuery->clone()->where('status', false)->count(),
        ];

        // Apply status filter
        if (!empty($statusFilter)) {
            $query->where(function($q) use ($statusFilter) {
                foreach ($statusFilter as $status) {
                    if ($status === 'active') {
                        $q->orWhere('status', true);
                    } elseif ($status === 'inactive') {
                        $q->orWhere('status', false);
                    }
                }
            });
        }

        // Apply building filter
        if (!empty($buildingFilter)) {
            $query->whereIn('property_id', $buildingFilter);
        }

        // Apply group filter
        if (!empty($groupFilter)) {
            $hasNoneFilter = in_array('none', $groupFilter);
            $groupIds = array_filter($groupFilter, function($id) { return $id !== 'none'; });

            if ($hasNoneFilter && !empty($groupIds)) {
                // Include both contacts with no groups AND contacts in specific groups
                $query->where(function($q) use ($groupIds) {
                    $q->whereDoesntHave('groups')
                      ->orWhereHas('groups', function ($subQ) use ($groupIds) {
                          $subQ->whereIn('groups.id', $groupIds);
                      });
                });
            } elseif ($hasNoneFilter) {
                // Only contacts with no groups
                $query->whereDoesntHave('groups');
            } elseif (!empty($groupIds)) {
                // Only contacts in specific groups
                $query->whereHas('groups', function ($q) use ($groupIds) {
                    $q->whereIn('groups.id', $groupIds);
                });
            }
        }

        $tenants = $query->orderBy($sort['field'], $sort['direction'])
                        ->paginate($perPage)
                        ->withQueryString();

        // Get properties with tenant counts
        $properties = Property::select('id', 'name')
            ->withCount('tenants')
            ->orderBy('name')
            ->get()
            ->map(function ($property) {
                return [
                    'id' => $property->id,
                    'name' => $property->name,
                    'tenantCount' => $property->tenants_count
                ];
            });

        // Get groups with tenant counts
        $groups = Group::select('id', 'name', 'color')
            ->where('status', true)
            ->withCount('tenants')
            ->orderBy('name')
            ->get()
            ->map(function ($group) {
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'color' => $group->color,
                    'tenantCount' => $group->tenants_count
                ];
            });

        // Get count of tenants with no groups
        $noGroupCount = Tenant::whereDoesntHave('groups')->count();

        // Return JSON for AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'tenants' => $tenants,
                'statusCounts' => $statusCounts,
                'properties' => $properties,
                'groups' => $groups,
                'noGroupCount' => $noGroupCount,
            ]);
        }

        return Inertia::render('Tenants/Index', [
            'tenants' => $tenants,
            'sort' => $sort,
            'filters' => [
                'search' => $search,
                'status' => $statusFilter,
                'buildings' => $buildingFilter,
                'groups' => $groupFilter,
                'per_page' => $perPage
            ],
            'statusCounts' => $statusCounts,
            'properties' => $properties,
            'groups' => $groups,
            'noGroupCount' => $noGroupCount,
        ]);
    }

    public function show(Tenant $tenant)
    {
        // Load tenant with relationships and message log history
        $tenant->load(['property', 'groups']);

        // Get recent message log history for this tenant
        $messageHistory = $tenant->messageRecipients()
            ->with(['message' => function ($query) {
                $query->select('id', 'title', 'type', 'channels', 'subject', 'content', 'sms_content', 'email_content', 'whatsapp_content', 'created_at');
            }])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($recipient) {
                $message = $recipient->message;
                $channel = $recipient->channel ?? 'unknown';

                // Get the appropriate content for this channel
                $content = null;
                if ($message) {
                    $content = match($channel) {
                        'sms' => $message->sms_content,
                        'email' => $message->email_content,
                        'whatsapp' => $message->whatsapp_content,
                        default => $message->content // fallback to legacy content
                    };
                }

                return [
                    'id' => $recipient->id,
                    'message_id' => $message->id ?? null,
                    'message_title' => $message->title ?? 'Untitled Message',
                    'message_type' => $channel,
                    'message_subject' => $message->subject ?? null,
                    'message_content' => $content,
                    'status' => $recipient->status,
                    'recipient_type' => $recipient->recipient_type,
                    'recipient_value' => $recipient->recipient_value,
                    'sent_at' => $recipient->sent_at,
                    'delivered_at' => $recipient->delivered_at,
                    'error_message' => $recipient->error_message,
                    'created_at' => $recipient->created_at,
                ];
            });

        return Inertia::render('Tenants/Show', [
            'contact' => $tenant,
            'messageHistory' => $messageHistory,
        ]);
    }

    public function create()
    {
        $properties = Property::select('id', 'name')->orderBy('name')->get();
        $groups = Group::select('id', 'name', 'color')->where('status', true)->orderBy('name')->get();

        return Inertia::render('Tenants/TenantForm', [
            'properties' => $properties,
            'groups' => $groups,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'secondary_email' => 'nullable|email|max:255',
            'mobile_phone' => 'nullable|string|max:255',
            'contact_sms' => 'required|boolean',
            'contact_wa' => 'required|boolean',
            'contact_email' => 'required|boolean',
            'property_id' => 'required|exists:properties,id',
            'unit_number' => 'nullable|string|max:255',
            'status' => 'required|boolean',
            'groups' => 'nullable|array',
            'groups.*' => 'exists:groups,id',
        ]);

        $groups = $validated['groups'] ?? [];
        unset($validated['groups']);

        $tenant = Tenant::create($validated);

        // Sync groups
        if (!empty($groups)) {
            $tenant->groups()->sync($groups);
        }

        return redirect()->route('tenants.index')->with('success', 'Tenant created successfully.');
    }

    public function edit(Tenant $tenant)
    {
        $properties = Property::select('id', 'name')->orderBy('name')->get();
        $groups = Group::select('id', 'name', 'color')->where('status', true)->orderBy('name')->get();

        return Inertia::render('Tenants/TenantForm', [
            'tenant' => $tenant->load(['property', 'groups']),
            'properties' => $properties,
            'groups' => $groups,
        ]);
    }

    public function update(Request $request, Tenant $tenant)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'secondary_email' => 'nullable|email|max:255',
            'mobile_phone' => 'nullable|string|max:255',
            'secondary_mobile_phone' => 'nullable|string|max:255',
            'whatsapp_number' => 'nullable|string|max:255',
            'secondary_whatsapp_number' => 'nullable|string|max:255',
            'contact_sms' => 'required|boolean',
            'contact_wa' => 'required|boolean',
            'contact_email' => 'required|boolean',
            'property_id' => 'required|exists:properties,id',
            'unit_number' => 'nullable|string|max:255',
            'status' => 'required|boolean',
            'groups' => 'nullable|array',
            'groups.*' => 'exists:groups,id',
        ]);

        $groups = $validated['groups'] ?? [];
        unset($validated['groups']);

        if($tenant->update($validated)) {
            // Sync groups
            $tenant->groups()->sync($groups);
            return redirect()->route('tenants.index')->with('success', 'Tenant updated successfully.');
        }

        return redirect()->back()->with('error', 'Failed to update tenant.');
    }

    public function destroy(Tenant $tenant)
    {
        try {
            $tenant->delete();
            return redirect()->route('tenants.index')->with('success', 'Tenant deleted successfully.');
        } catch (\Exception) {
            return redirect()->route('tenants.index')->with('error', 'Failed to delete tenant.');
        }
    }

    public function messageHistory(Request $request, Tenant $tenant)
    {
        $search = $request->input('search');
        $type = $request->input('type');
        $status = $request->input('status');
        $date = $request->input('date');
        $perPage = $request->input('per_page', 10);

        $query = $tenant->messageRecipients()
            ->with(['message' => function ($query) {
                $query->select('id', 'title', 'type', 'channels', 'subject', 'content', 'sms_content', 'email_content', 'whatsapp_content', 'created_at');
            }]);

        // Apply search filter
        if ($search) {
            $query->whereHas('message', function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Apply type filter
        if ($type) {
            $query->whereHas('message', function ($q) use ($type) {
                $q->where('type', $type);
            });
        }

        // Apply status filter
        if ($status) {
            $query->where('status', $status);
        }

        // Apply date filter
        if ($date) {
            $now = now();
            switch ($date) {
                case 'today':
                    $query->whereDate('created_at', $now->toDateString());
                    break;
                case 'yesterday':
                    $yesterday = $now->copy()->subDay();
                    $query->whereDate('created_at', $yesterday->toDateString());
                    break;
                case 'week':
                    $startOfWeek = $now->copy()->startOfWeek();
                    $endOfWeek = $now->copy()->endOfWeek();
                    $query->whereBetween('created_at', [$startOfWeek, $endOfWeek]);
                    break;
                case 'month':
                    $startOfMonth = $now->copy()->startOfMonth();
                    $endOfMonth = $now->copy()->endOfMonth();
                    $query->whereBetween('created_at', [$startOfMonth, $endOfMonth]);
                    break;
                case 'year':
                    $startOfYear = $now->copy()->startOfYear();
                    $endOfYear = $now->copy()->endOfYear();
                    $query->whereBetween('created_at', [$startOfYear, $endOfYear]);
                    break;
            }
        }

        // Apply custom date range filter
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');

        if ($dateFrom && $dateTo) {
            $query->whereBetween('created_at', [
                \Carbon\Carbon::parse($dateFrom)->startOfDay(),
                \Carbon\Carbon::parse($dateTo)->endOfDay()
            ]);
        } elseif ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        } elseif ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        $messages = $query->orderBy('created_at', 'desc')
            ->paginate($perPage)
            ->through(function ($recipient) {
                $message = $recipient->message;
                $channel = $recipient->channel ?? 'unknown';

                // Get the appropriate content for this channel
                $content = null;
                if ($message) {
                    $content = match($channel) {
                        'sms' => $message->sms_content,
                        'email' => $message->email_content,
                        'whatsapp' => $message->whatsapp_content,
                        default => $message->content // fallback to legacy content
                    };
                }

                return [
                    'id' => $recipient->id,
                    'message_id' => $message->id ?? null,
                    'message_title' => $message->title ?? 'Untitled Message',
                    'message_type' => $channel,
                    'message_subject' => $message->subject ?? null,
                    'message_content' => $content,
                    'status' => $recipient->status,
                    'recipient_type' => $recipient->recipient_type,
                    'recipient_value' => $recipient->recipient_value,
                    'sent_at' => $recipient->sent_at,
                    'delivered_at' => $recipient->delivered_at,
                    'error_message' => $recipient->error_message,
                    'created_at' => $recipient->created_at,
                ];
            });

        return response()->json($messages);
    }
}
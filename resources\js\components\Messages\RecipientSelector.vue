<template>
  <div class="grid gap-4 relative" style="z-index: 40">
    <!-- Recipient Mode Selection -->
    <div class="relative" style="z-index: 40">
      <div class="mt-2">
        <RadioGroup v-model="recipientMode" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <label
            class="flex items-center space-x-2 rounded-lg border p-4 cursor-pointer hover:bg-muted/50"
            :class="{ 'border-primary': recipientMode === 'all' }"
            @click="recipientMode = 'all'"
          >
            <RadioGroupItem value="all" />
            <div class="grid gap-1">
              <span class="font-medium text-sm">Send to All Tenants</span>
              <p class="text-sm text-muted-foreground">
                Message will be sent to all active tenants ({{ totalTenantCount }} total)
              </p>
            </div>
          </label>
          <label 
            class="flex items-center space-x-2 rounded-lg border p-4 cursor-pointer hover:bg-muted/50"
            :class="{ 'border-primary': recipientMode === 'specific' }"
            @click="recipientMode = 'specific'"
          >
            <RadioGroupItem value="specific" />
            <div class="grid gap-1">
              <span class="font-medium text-sm">Select Specific Recipients</span>
              <p class="text-sm text-muted-foreground">
                Choose individual tenants or properties to send the message to
              </p>
            </div>
          </label>
        </RadioGroup>
      </div>
    </div>
    
    <Separator />
    
    <!-- Recipient Summary -->
    <div class="text-sm text-muted-foreground flex items-center gap-2">
      <Users class="h-4 w-4" />
      {{ recipientSummary }}
    </div>

    <!-- Individual Recipient Selection -->
    <RecipientSearch
      v-if="recipientMode === 'specific'"
      v-model:recipients="recipients"
      :message-channels="messageChannels"
      :tenants="tenants"
      :properties="properties"
      :groups="groups"
    />
    
    <InputError :message="error" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Users } from 'lucide-vue-next';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import InputError from '@/components/InputError.vue';
import RecipientSearch from './RecipientSearch.vue';

interface Recipient {
  type: string;
  id: string;
  name: string;
}

interface Tenant {
  id: number;
  name: string;
  email: string;
  phone: string;
  property?: string;
  contact_sms: boolean;
  contact_email: boolean;
  contact_wa: boolean;
}

interface Property {
  id: number;
  name: string;
  tenants_count: number;
}

interface Group {
  id: number;
  name: string;
  tenants_count: number;
}

interface Props {
  recipients: Recipient[];
  recipientMode: string;
  messageChannels: string[];
  tenants: Tenant[];
  properties: Property[];
  groups?: Group[];
  totalTenantCount: number;
  error?: string;
}

interface Emits {
  (e: 'update:recipients', value: Recipient[]): void;
  (e: 'update:recipient-mode', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const recipients = computed({
  get: () => props.recipients,
  set: (value: Recipient[]) => emit('update:recipients', value)
});

const recipientMode = computed({
  get: () => props.recipientMode,
  set: (value: string) => emit('update:recipient-mode', value)
});

const recipientSummary = computed(() => {
  if (props.recipientMode === 'all') {
    return `All tenants will be included (${props.totalTenantCount} total)`;
  }
  return props.recipients.length ? `${props.recipients.length} recipients selected` : 'No recipients selected';
});
</script>

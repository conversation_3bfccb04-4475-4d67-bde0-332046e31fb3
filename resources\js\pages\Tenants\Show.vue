<template>
  <AppLayout :breadcrumbs="breadcrumbs">

    <Head :title="`Tenant: ${tenant.full_name}`" />

    <div class="flex h-full flex-1 flex-col gap-6 p-6">
      <!-- Header Section -->
      <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div class="space-y-4">
          <Heading :title="tenant.full_name"
            :description="`View and manage tenant information for ${tenant.full_name}`" />
          <div class="flex items-center gap-4 text-sm text-foreground">
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 rounded-full" :class="tenant.status ? 'bg-green-500' : 'bg-gray-400'"></span>
              {{ tenant.status ? 'Active Tenant' : 'Inactive Tenant' }}
            </span>
            <span class="flex items-center gap-1">
              <span class="h-2 w-2 bg-blue-500 rounded-full"></span>
              ID: #{{ tenant.id }}
            </span>
            <span v-if="tenant.property" class="flex items-center gap-1">
              <span class="h-2 w-2 bg-purple-500 rounded-full"></span>
              {{ tenant.property.name }}{{ tenant.unit_number ? ` - Unit ${tenant.unit_number}` : '' }}
            </span>
            <span v-if="tenant.groups && tenant.groups.length > 0" class="flex items-center gap-1">
              <span class="h-2 w-2 bg-orange-500 rounded-full"></span>
              {{ tenant.groups.length }} {{ tenant.groups.length === 1 ? 'group' : 'groups' }}
            </span>
          </div>
        </div>
        <div class="flex flex-col gap-3 sm:flex-row">
          <Button variant="outline" size="lg" class="shadow-sm hover:shadow-md transition-all duration-200" asChild>
            <Link :href="`/tenants/${tenant.id}/edit`">
            <Pencil class="mr-2 h-5 w-5" />
            Edit Tenant
            </Link>
          </Button>
          <Button size="lg" class="shadow-lg hover:shadow-xl transition-all duration-200" asChild>
            <Link :href="`/messages/create?tenant_id=${tenant.id}`">
            <MessageSquare class="mr-2 h-5 w-5" />
            Send Message
            </Link>
          </Button>
        </div>
      </div>

      <FlashAlert />

      <!-- Tenant Information -->
      <div class="grid gap-8 lg:grid-cols-3">
        <!-- Main Content with Tabs -->
        <div class="lg:col-span-2">
          <!-- Tabs for Tenant Info and Message Log -->
          <div class="bg-card rounded-lg border">
            <div class="border-b">
              <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button @click="activeTab = 'tenant-info'" :class="[
                  'py-4 px-1 border-b-2 font-medium text-sm cursor-pointer',
                  activeTab === 'tenant-info'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                ]">
                  Tenant Information
                </button>
                <button @click="activeTab = 'message-log'" :class="[
                  'py-4 px-1 border-b-2 font-medium text-sm cursor-pointer',
                  activeTab === 'message-log'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                ]">
                  Message Log
                </button>
              </nav>
            </div>

            <!-- Tenant Information Tab -->
            <div v-if="activeTab === 'tenant-info'" class="p-6">
              <div class="space-y-6">
                <!-- Basic Information -->
                <Card class="card-primary">
                  <CardHeader class="card-header-primary">
                    <CardTitle class="card-title-primary">
                      <User class="card-title-icon" />
                      Basic Information
                    </CardTitle>
                    <CardDescription>
                      Personal details and identification information
                    </CardDescription>
                  </CardHeader>
                  <CardContent class="space-y-4">
                    <div class="grid gap-4 sm:grid-cols-2">
                      <!-- First Name -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <User class="h-4 w-4" />
                          First Name
                        </span>
                        <span class="text-sm font-medium">{{ tenant.first_name || '-' }}</span>
                      </div>

                      <!-- Last Name -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <User class="h-4 w-4" />
                          Last Name
                        </span>
                        <span class="text-sm font-medium">{{ contact.last_name || '-' }}</span>
                      </div>

                      <!-- Tenant ID -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <Hash class="h-4 w-4" />
                          Tenant ID
                        </span>
                        <span class="text-sm font-medium">#{{ contact.id }}</span>
                      </div>

                      <!-- Property -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <Building class="h-4 w-4" />
                          Property
                        </span>
                        <span class="text-sm font-medium">{{ contact.property?.name || '-' }}</span>
                      </div>

                      <!-- Unit Number -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <Hash class="h-4 w-4" />
                          Unit Number
                        </span>
                        <span class="text-sm font-medium">{{ contact.unit_number || '-' }}</span>
                      </div>

                      <!-- Status -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <ToggleLeft class="h-4 w-4" />
                          Status
                        </span>
                        <div v-if="contact.status"
                          class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                          <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                          Active
                        </div>
                        <div v-else
                          class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                          <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                          Inactive
                        </div>
                      </div>

                      <!-- Created Date -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <Calendar class="h-4 w-4" />
                          Created
                        </span>
                        <span class="text-sm font-medium">
                          <DateTime :date="contact.created_at" />
                        </span>
                      </div>


                    </div>
                  </CardContent>
                </Card>

                <!-- Contact Methods -->
                <Card class="card-secondary">
                  <CardHeader class="card-header-secondary">
                    <CardTitle class="card-title-secondary">
                      <Phone class="card-title-icon" />
                      Contact Methods
                    </CardTitle>
                    <CardDescription>
                      Available communication channels and contact information
                    </CardDescription>
                  </CardHeader>
                  <CardContent class="space-y-4">
                    <div class="grid gap-4 sm:grid-cols-2">
                      <!-- Primary Phone -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <Phone class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          Primary Phone
                        </span>
                        <span class="text-sm font-medium">{{ contact.mobile_phone || '-' }}</span>
                      </div>

                      <!-- Secondary Phone -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <Phone class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          Secondary Phone
                        </span>
                        <span class="text-sm font-medium">{{ contact.secondary_mobile_phone || '-' }}</span>
                      </div>

                      <!-- Primary Email -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <Mail class="h-4 w-4 text-amber-600 dark:text-amber-400" />
                          Primary Email
                        </span>
                        <span class="text-sm font-medium">{{ contact.email || '-' }}</span>
                      </div>

                      <!-- Secondary Email -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <Mail class="h-4 w-4 text-amber-600 dark:text-amber-400" />
                          Secondary Email
                        </span>
                        <span class="text-sm font-medium">{{ contact.secondary_email || '-' }}</span>
                      </div>

                      <!-- Primary WhatsApp -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <MessageCircle class="h-4 w-4 text-green-600 dark:text-green-400" />
                          Primary WhatsApp
                        </span>
                        <span class="text-sm font-medium">{{ contact.whatsapp_number || '-' }}</span>
                      </div>

                      <!-- Secondary WhatsApp -->
                      <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                        <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                          <MessageCircle class="h-4 w-4 text-green-600 dark:text-green-400" />
                          Secondary WhatsApp
                        </span>
                        <span class="text-sm font-medium">{{ contact.secondary_whatsapp_number || '-' }}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>


              </div>
            </div>

            <!-- Message Log Tab -->
            <div v-if="activeTab === 'message-log'" class="p-6">
              <MessageLogTab :tenant="tenant" />
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Details Card -->
          <Card class="sidebar-card-details">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Info class="h-4 w-4" />
                Details
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content">
              <div class="space-y-3">
                <!-- Tenant ID -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Hash class="h-4 w-4" />
                    Tenant ID
                  </span>
                  <span class="text-sm font-medium">#{{ contact.id }}</span>
                </div>

                <!-- Created -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Created
                  </span>
                  <span class="text-sm font-medium">
                    <DateTime :date="contact.created_at" type="absolute" />
                  </span>
                </div>



                <!-- Status -->
                <div class="flex items-center justify-between py-2">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <ToggleLeft class="h-4 w-4" />
                    Status
                  </span>
                  <div v-if="contact.status"
                    class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                    Active
                  </div>
                  <div v-else
                    class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                    <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                    Inactive
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Groups -->
          <Card v-if="contact.groups && contact.groups.length > 0" class="sidebar-card-details">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Users class="h-4 w-4" />
                Groups
              </CardTitle>
              <CardDescription>
                Groups this tenant belongs to
              </CardDescription>
            </CardHeader>
            <CardContent class="sidebar-card-content">
              <div class="flex flex-wrap gap-2">
                <div v-for="group in contact.groups" :key="group.id"
                  class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border" :style="{
                    backgroundColor: group.color,
                    borderColor: group.color,
                    color: 'white'
                  }">
                  {{ group.name }}
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Communication Preferences -->
          <Card class="sidebar-card-details">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Settings class="h-4 w-4" />
                Communication Preferences
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content">
              <div class="space-y-3">
                <!-- SMS -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <MessageSquare class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    SMS Messages
                  </span>
                  <div v-if="contact.contact_sms"
                    class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                    Enabled
                  </div>
                  <div v-else
                    class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                    <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                    Disabled
                  </div>
                </div>

                <!-- Email -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Mail class="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    Email Messages
                  </span>
                  <div v-if="tenant.contact_email"
                    class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                    Enabled
                  </div>
                  <div v-else
                    class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                    <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                    Disabled
                  </div>
                </div>

                <!-- WhatsApp -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <MessageCircle class="h-4 w-4 text-green-600 dark:text-green-400" />
                    WhatsApp Messages
                  </span>
                  <div v-if="tenant.contact_wa"
                    class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                    Enabled
                  </div>
                  <div v-else
                    class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                    <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                    Disabled
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>


  </AppLayout>
</template>

<script setup lang="ts">

import { ref, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import {
  Pencil,
  MessageSquare,
  Mail,
  MessageCircle,
  User,
  Users,
  Phone,
  Building,
  Hash,
  ToggleLeft,
  Calendar,
  Settings,
  Zap,
  Info
} from 'lucide-vue-next';
import DateTime from '@/components/ui/DateTime.vue';
import MessageLogTab from '@/components/Tenants/MessageLogTab.vue';
import type { Tenant } from '@/types/tenant';
import type { BreadcrumbItem } from '@/types';

interface MessageHistoryItem {
  id: number;
  message_id?: number;
  message_title: string;
  message_type: string;
  message_subject?: string;
  message_content?: string;
  status: string;
  recipient_type: string;
  recipient_value: string;
  sent_at?: string;
  delivered_at?: string;
  error_message?: string;
  created_at: string;
}

interface Props {
  tenant: Tenant;
  messageHistory: MessageHistoryItem[];
}

const props = defineProps<Props>();

// Template compatibility
const contact = computed(() => props.tenant);

// State
const activeTab = ref<'tenant-info' | 'message-log'>('tenant-info');

const breadcrumbs: BreadcrumbItem[] = [
  { title: 'Tenants', href: '/tenants' },
  { title: props.tenant.full_name, href: '#' },
];


</script>
